<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '用户协议',
  },
}
</route>

<script setup lang="ts">
const statusBarHeight = uni.getSystemInfoSync().statusBarHeight + 'px'
const navTo = (url: string) => {
  uni.reLaunch({
    url,
  })
}
</script>
<template>
  <view class="page-protocol">
    <wd-navbar :bordered="false" placeholder safeAreaInsetTop>
      <template #left>
        <image
          src="@/static/images/arrow-left.png"
          mode="scaleToFill"
          class="icon-back"
          @click="navTo('/pages/login/index')"
        ></image>
      </template>
      <template #title>
        <text class="title">用户协议</text>
      </template>
    </wd-navbar>
    <view class="split-line"></view>
    <view class="content">
      <view class="card-list">
        <ProtocolTemplate></ProtocolTemplate>
      </view>
    </view>
  </view>
</template>
<style lang="scss" scoped>
.page-protocol {
  height: 100%;
  .content {
    height: calc(100% - v-bind(statusBarHeight) - 45px);
    .card-list {
      height: 100%;
    }
  }
}
</style>
