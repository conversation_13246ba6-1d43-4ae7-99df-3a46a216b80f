<script lang="ts" setup>
import { deepClone } from 'wot-design-uni/components/common/util'

const props = withDefaults(
  defineProps<{
    leftText?: string
    title?: string
    rightText?: string
    resetText?: string
    orgList?: Array<any>
  }>(),
  {
    leftText: '取消',
    title: '筛选',
    rightText: '确定',
    resetText: '重置',
    orgList: () => [],
  },
)

const emit = defineEmits(['confirm'])

const popup = ref()
const form = ref({
  orgIdList: [],
  dateRange: '',
  createdAtLeft: '',
  createdAtRight: '',
  defaultOrg: [],
})

const popupVisible = ref(false)
const orgTreePicker = ref()
const dateRangePicker = ref()
const defaultOrg = ref([])
const currMenu = ref('')
const menuList = [
  { name: '发布单位', props: 'orgIdList' },
  { name: '发布日期', props: 'dateRange' },
]

const openPopup = (data: any) => {
  currMenu.value = 'orgIdList'
  form.value = deepClone(data)
  defaultOrg.value = data.defaultOrg || []
  popupVisible.value = true
}

const closePopup = () => {
  popupVisible.value = false
}

const confirmPopup = () => {
  popupVisible.value = false
  const params = deepClone(form.value)
  if (params.dateRange) {
    params.createdAtLeft = params.dateRange[0] + ' 00:00:00'
    params.createdAtRight = params.dateRange[1] + ' 23:59:59'
  } else {
    params.createdAtLeft = ''
    params.createdAtRight = ''
  }

  currMenu.value = null
  emit('confirm', params)
}

const handleMenuClick = (value: string) => {
  currMenu.value = value
}

const confirmOrg = (val: SelectOption[]) => {
  form.value.orgIdList = val
  defaultOrg.value = val
  form.value.defaultOrg = val
}

const confirmDateRange = (val: string) => {
  form.value.dateRange = val
}

const resetPopup = () => {
  form.value = {
    orgIdList: [],
    dateRange: '',
    createdAtLeft: '',
    createdAtRight: '',
    defaultOrg: [],
  }
  defaultOrg.value = []
  if (currMenu.value === 'orgIdList' && popupVisible.value) {
    nextTick(() => {
      orgTreePicker.value.reset()
    })
  }
}

defineExpose({ openPopup })
</script>

<template>
  <view class="high-search">
    <wd-popup
      ref="popup"
      v-model="popupVisible"
      lock-scroll
      position="bottom"
      :close-on-click-modal="false"
      :safe-area-inset-bottom="true"
    >
      <view class="popup-header">
        <view class="left">
          <text @click="closePopup" class="text">{{ leftText }}</text>
        </view>
        <view class="center">
          <text class="title">{{ title }}</text>
        </view>
        <view class="right">
          <text @click="resetPopup" class="reset">{{ resetText }}</text>
          <text @click="confirmPopup" class="text">{{ rightText }}</text>
        </view>
      </view>
      <view class="split-line"></view>
      <view class="popup-body">
        <view class="aside-menu">
          <view
            :class="['menu-item', currMenu === item.props ? 'active-menu' : '']"
            v-for="(item, index) in menuList"
            :key="index"
            @click="handleMenuClick(item.props)"
          >
            <text class="text">{{ item.name }}</text>
          </view>
        </view>
        <view class="content">
          <TreePickerView
            ref="categoryTreePicker"
            :localdata="orgList"
            :multiple="true"
            :selectedData="defaultOrg"
            @confirm="confirmOrg"
            v-if="currMenu === 'orgIdList' && popupVisible"
          ></TreePickerView>

          <uni-datetime-picker
            ref="dateRangePicker"
            type="daterange"
            v-model="form.dateRange"
            @change="
              (val: string) => {
                confirmDateRange(val)
              }
            "
            v-if="currMenu === 'dateRange'"
          />
        </view>
      </view>
    </wd-popup>
  </view>
</template>
