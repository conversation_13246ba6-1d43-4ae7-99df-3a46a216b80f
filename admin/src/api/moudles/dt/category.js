import { CTX } from "@/api/config";
import http from "@/utils/request";
const PREFIX = CTX.DT + `dtCategory`;
export default {
	categoryPage: {
		name: "分页",
		get: async function (params) {
			return await http.get(PREFIX + `/page`, params);
		},
	},

	categoryAdd: {
		name: "新增",
		post: async function (data) {
			return await http.post(PREFIX + `/add`, data);
		},
	},

	categoryEdit: {
		name: "编辑",
		put: async function (data) {
			return await http.put(PREFIX + `/edit`, data);
		},
	},

	category: {
		name: "详情",
		get: async function (id) {
			return await http.get(PREFIX + `/` + id);
		},
	},

	categoryBatchDelete: {
		name: "批量删除",
		delete: async function (data) {
			return await http.delete(PREFIX + `/delete`, data);
		},
	},

	categoryListAll: {
		name: "全部数据",
		get: async function () {
			return await http.get(PREFIX + `/listAll`);
		},
	},

	getTree: {
		name: "分类设置-分类列表",
		get: async function (params) {
			const t = params.type ? params.type : "OC";
			return await http.get(
				`${PREFIX}/getTree`,
				params
			);
		},
	},
};
