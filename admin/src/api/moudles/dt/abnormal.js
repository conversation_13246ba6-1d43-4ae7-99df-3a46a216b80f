/**
 * 等级录入
 */
import { CTX } from "@/api/config";
import http from "@/utils/request";

const PREFIX = CTX.DT + `abnormal`;
export default {
	// 登记录入
	page: {
		name: "分页",
		get: async function (params) {
			return await http.get(`${PREFIX}/page`, params);
		},
	},
	// 新增
	add: {
		name: "新增",
		post: async function (data) {
			return await http.post(`${PREFIX}/add`, data);
		},
	},
	// 根据传入的Id，删除数据 ids
	delete: {
		name: "根据传入的Id，删除数据",
		delete: async function (data) {
			return await http.delete(`${PREFIX}/delete`, data);
		},
	},
	// 编辑
	edit: {
		name: "编辑",
		post: async function (data) {
			return await http.post(`${PREFIX}/edit`, data);
		},
	},
	// 获取所有数据
	listAll: {
		name: "获取所有数据",
		get: async function () {
			return await http.post(`${PREFIX}/listAll`);
		},
	},
	// 根据id获取数据
	getById: {
		name: "根据id获取数据",
		get: async function (id) {
			return await http.get(`${PREFIX}/${id}`);
		},
	},
	// 获取详情
	getWithDetail: {
		name: "获取详情",
		get: async function (id) {
			return await http.get(`${PREFIX}/getWithDetail/${id}`);
		},
	},
	dealAbnormal: {
		name: "编辑",
		post: async function (data) {
			return await http.post(PREFIX + `/dealAbnormal`, data);
		},
	},

	pageAbnormal: {
		name: "分页",
		get: async function (params) {
			params.typeNotLike='close';
			return await http.get(`${PREFIX}/page`, params);
		},
	},

	close: {
		name: "close",
		get: async function (id) {
			return await http.get(`${PREFIX}/close/${id}`);
		}
	}
};
