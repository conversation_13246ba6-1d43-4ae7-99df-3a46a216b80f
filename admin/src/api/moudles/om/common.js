import common from "@/config/common";

export default {
	// 设备维护状态
	deviceStatus: [
		{ label: "草稿", value: "DRAFT" },
		{ label: "启用", value: "ENABLED" },
		{ label: "停用", value: "DISABLED" },
	],

	// 设备运行状态
	deviceRunStatus: [
		{ label: "运行", value: "ENABLED" },
		{ label: "停止", value: "DISABLED" },
	],

	// 设备运行状态
	patrolPlanStatus: [
		{ label: "运行中", value: "ENABLED" },
		{ label: "已停止", value: "DISABLED" },
	],

	// 巡检异常状态
	patrolAbnormalStatus: [
		{ label: "待处理", value: "ENABLED" },
		{ label: "已处理", value: "DISABLED" },
	],

	// 巡检异常处理方式
	patrolAbnormalDealType: [
		{ label: "直接关闭", value: "CLOSED" },
		{ label: "报警登记", value: "ALERTED" },
		{ label: "报修登记", value: "REPAIRED" },
	],

	// 巡检异常处理方式
	patrolledType: [
		{ label: "已打卡", value: true },
		{ label: "缺卡", value: false },
	],

	// 警报状态
	alertStatus: [
		{ label: "待处理", value: "HANDLE" },
		{ label: "处理中", value: "HANDLING" },
		{ label: "已处理", value: "HANDLED" },
		{ label: "已关闭", value: "CLOSED" },
	],
	alertResultType: [
		{ label: "报警确认", value: "APCL" },
		{ label: "处理完毕", value: "CLWB" },
		{ label: "关闭报警", value: "GBBJ" },
	],
	//deviceMate数据类型
	deviceMateType: [
		{ label: "温度", value: "NUMBER" },
		{ label: "湿度", value: "NUMBER" },
		{ label: "开关状态", value: "BOOLEAN" },
		{ label: "报警状态", value: "BOOLEAN" },
		{ label: "运行状态", value: "BOOLEAN" },
	],

	// 打卡类型
	checkType: [{ label: "网页打卡", value: "WEB" }],
	// 报警推送
	alertType: [
		{ label: "推送", value: "ENABLED" },
		{ label: "不推送", value: "DISABLED" },
	],
	// 设备维护状态
	ruleStatus: [
		{ label: "启用", value: "ENABLED" },
		{ label: "停用", value: "DISABLED" },
	],

	searchKeys(obj, value, name) {
		return common.searchKeys(obj, value, name);
	},
	searchDeviceStatus(value, name) {
		return this.searchKeys(this.deviceStatus, value, name);
	},

	searchDeviceApproveStatus(value, name) {
		return this.searchKeys(this.deviceApproveStatus, value, name);
	},

	searchDictValue(obj, value) {
		return this.searchKeys(obj, value, "dictValue");
	},
};
