import { CTX } from "@/api/config";
import http from "@/utils/request";

const PREFIX = CTX.OM + `category`;
const BUILDING_TYPE = "BU";
export default {
	page: {
		name: "分类设置-分类列表 启用状态类别",
		get: async function (type) {
			const t = type ? type : "OC";
			return await http.get(`${PREFIX}/getEnabledCategoryTree/${t}`);
		},
	},
	listAll: ({ type }) => {
		const t = type;
		return {
			name: "分类设置-分类列表",
			get: async function (params) {
				return await http.get(
					`${PREFIX}/getAllCategoryTree/${t}`,
					params
				);
			},
		};
	},
	listEnabledAll: {
		name: "分类设置-分类列表",
		get: async function (params) {
			const t = params.type ? params.type : "OC";
			return await http.get(
				`${PREFIX}/getEnabledCategoryTree/${t}`,
				params
			);
		},
	},
	pageSimple: {
		name: "简单搜索 分页，可带keyword模糊搜索",
		get: async function (params) {
			params.pageSize = 1000;
			params.pageNum = 1;
			return await http.get(`${PREFIX}/pageSimple`, params);
		},
	},
	getById: {
		name: "分类设置-根据id查询",
		get: async function (data) {
			return await http.get(`${PREFIX}/${data}`);
		},
	},
	add: {
		name: "分类设置-类别新增",
		post: async function (data) {
			let type = data.type;
			if (!type) type = "OC";
			return await http.post(`${PREFIX}/${type}/add`, data);
		},
	},
	edit: {
		name: "分类设置-编辑类别（状态通用）",
		post: async function (data) {
			if (!data.id) return;
			return await http.post(`${PREFIX}/edit`, data);
		},
	},
	updateShowOrder: {
		name: "分类设置-批量更新排序-保存排序",
		post: async function (data) {
			return await http.post(PREFIX + `/updateShowOrder`, data);
		},
	},
	delete: {
		name: "批量删除",
		delete: async function (data) {
			return await http.delete(PREFIX + `/delete`, data);
		},
	},
	getBuildingTree: {
		name: "分类设置-楼宇树",
		get: async function () {
			const t = BUILDING_TYPE;
			return await http.get(`${PREFIX}/getAllCategoryTree/${t}`);
		},
	},
	getEnabledBuildingTree: {
		name: "楼宇树-状态为ENABLED",
		get: async function (param) {
			return await http.get(
				`${PREFIX}/getEnabledCategoryTree/${BUILDING_TYPE}`,
				param
			);
		},
	},
	move: {
		name: "移动",
		post: async function (data) {
			return await http.post(PREFIX + `/move`, data);
		},
	},
	listAllLet: {
		name: "分类设置-分类列表",
		get: async function ({ type }) {
			const t = type ? type : "CA";
			return await http.get(`${PREFIX}/getAllCategoryTree/${t}`);
		},
	},
	getCategoryTree: {
		name: "获取类别树",
		get: async function (type) {
			console.log(type, "232323");
			type = type ? type : "OCD";
			return await http.get(`${PREFIX}/getAllCategoryTree/` + type);
		},
	},
	getLevelOneByType: {
		name: "获取类型下一级类别",
		get: async function (type) {
			type = type ? type : "OCD";
			return await http.get(`${PREFIX}/getLevelOneByType/` + type);
		},
	},
	getChildes: {
		name: "获取类别子类",
		post: async function (params) {
			return await http.post(`${PREFIX}/getChildes`, params);
		},
	},

	getDevices: {
		name: "获取类别子类",
		get: async function (params) {
			const t = params.type ? params.type : "OCD";
			return await http.get(
				`${PREFIX}/getEnabledCategoryTree/${t}`,
				params
			);
		},
	},
};
