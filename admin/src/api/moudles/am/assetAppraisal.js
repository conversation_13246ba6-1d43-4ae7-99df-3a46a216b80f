import { CTX } from "@/api/config";
import http from "@/utils/request";

const PREFIX = CTX.AM + `assetAppraisal`;
export default {
	page: {
		name: "分页",
		get: async function (params) {
			return await http.get(`${PREFIX}/page`, params);
		},
	},
	pagePending: {
		name: "获取待处理分页",
		get: async function (params) {
			return await http.get(`${PREFIX}/page/pending`, params);
		},
	},
	add: {
		name: "新增",
		post: async function (data) {
			return await http.post(`${PREFIX}/add`, data);
		},
	},
	inspect: {
		name: "检修信息录入",
		post: async function (data) {
			return await http.post(`${PREFIX}/add/inspect`, data);
		},
	},
	edit: {
		name: "编辑",
		post: async function (data) {
			return await http.post(`${PREFIX}/edit`, data);
		},
	},
	delete: {
		name: "根据传入的Id，删除数据",
		delete: async function (data) {
			return await http.delete(`${PREFIX}/delete`, data);
		},
	},
	getWithDetail: {
		name: "根据id获取详情数据",
		get: async function (id) {
			return await http.get(`${PREFIX}/getWithDetail/${id}`);
		},
	},
	getById: {
		name: "根据id获取数据",
		get: async function (id) {
			return await http.get(`${PREFIX}/${id}`);
		},
	},
	listAll: {
		name: "根据id获取详情数据",
		get: async function () {
			return await http.get(`${PREFIX}/listAll`);
		},
	},
	complete: {
		name: "检定通过",
		post: async function (data) {
			return await http.post(`${PREFIX}/complete`, data);
		},
	},
	addExamine: {
		name: "检修通过",
		post: async function (data) {
			return await http.post(`${PREFIX}/repairSuccess`, data);
		},
	},
};
