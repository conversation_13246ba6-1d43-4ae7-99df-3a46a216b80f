/**
 * 等级录入
 */
import { CTX } from "@/api/config";
import http from "@/utils/request";

const PREFIX = CTX.AM + `assetInfo`;
export default {
	// 登记录入
	page: {
		name: "分页",
		get: async function (params) {
			return await http.get(`${PREFIX}/page`, params);
		},
	},
	// 台账分页
	accountPage: {
		name: "台账分页",
		get: async function (params) {
			return await http.get(`${PREFIX}/account/page`, params);
		},
	},
	// 新增
	add: {
		name: "新增",
		post: async function (data) {
			return await http.post(`${PREFIX}/add`, data);
		},
	},

	// 批量核对  IdList
	batchApprove: {
		name: "批量核对",
		post: async function (data) {
			return await http.post(`${PREFIX}/batch/approve`, data);
		},
	},
	// 根据传入的Id，删除数据 ids
	delete: {
		name: "根据传入的Id，删除数据",
		delete: async function (data) {
			return await http.post(`${PREFIX}/delete`, data);
		},
	},
	// 编辑
	edit: {
		name: "编辑",
		post: async function (data) {
			return await http.post(`${PREFIX}/edit`, data);
		},
	},
	// 获取详情
	getWithDetail: {
		name: "获取详情",
		get: async function (id) {
			return await http.get(`${PREFIX}/getWithDetail/${id}`);
		},
	},
	// 获取所有数据
	listAll: {
		name: "获取所有数据",
		get: async function () {
			return await http.post(`${PREFIX}/listAll`);
		},
	},
	// 暂停
	suspend: {
		name: "获取所有数据",
		post: async function (data) {
			return await http.post(`${PREFIX}/suspend`, data);
		},
	},
	// 根据id获取数据
	getById: {
		name: "根据id获取数据",
		get: async function (id) {
			return await http.get(`${PREFIX}/${id}`);
		},
	},
	enable: {
		name: "启用设备",
		post: async function (data) {
			return await http.post(PREFIX + `/enable/`, data);
		},
	},
	approve: {
		name: "核对数据",
		post: async function (data) {
			return await http.post(PREFIX + `/approve/`, data);
		},
	},
	applyAgain: {
		name: "重新录入",
		post: async function (data) {
			return await http.post(PREFIX + `/applyAgain/`, data);
		},
	},
	cancel: {
		name: "核对取消数据",
		post: async function (data) {
			return await http.post(PREFIX + `/cancel/`, data);
		},
	},
	reject: {
		name: "拒绝",
		post: async function (data) {
			return await http.post(PREFIX + `/reject/`, data);
		},
	},
	confirm: {
		name: "确认",
		post: async function (data) {
			return await http.post(PREFIX + `/confirm/`, data);
		},
	},
	queryTypeList: {
		name: "获取设备档案分类",
		get: async function () {
			return await http.get(PREFIX + `/account/queryTypeList`);
		},
	},
	dataCount: {
		name: "首页统计",
		get: async function () {
			return await http.get(PREFIX + `/dataCount`);
		},
	},
};
