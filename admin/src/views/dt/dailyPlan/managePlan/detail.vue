<template>
	<div class="drawer detail">
		<el-drawer
			v-model="visible"
			:size="640"
			custom-class="detail-drawer"
			destroy-on-close
			@closed="$emit('closed')"
		>
			<template #header>
				<div class="title">
					<div class="line"></div>
					<span>{{ title }}</span>
				</div>
			</template>
			<template #default>
				<div class="drawer-content detail">
					<el-form ref="editForm" :model="form" class="form-item">
						<div class="drawer-item">
							<div class="drawer-item-title">
								<img :src="drawerTitle" />
								<span>基础信息</span>
							</div>
						</div>
						<div class="out-flex-container">
							<el-form-item class="upload" prop="fileId">
								<el-image
									:preview-src-list="[
										solutionSrc(form.fileId),
									]"
									:src="solutionSrc(form.fileId)"
									alt="点击查看大图"
									preview-teleported
									style="width: 120px; height: 120px"
									title="点击查看大图"
								>
									<template #error>
										<el-image
											:src="$dmImageDefault"
											style="width: 120px; height: 120px"
										></el-image>
									</template>
								</el-image>
							</el-form-item>
							<div class="inner-flex-container">
								<el-form-item label="编号">
									{{ form.code }}
								</el-form-item>
								<el-form-item label="计划名称">
									{{ form.name }}
								</el-form-item>
								<el-form-item label="计划类型">
									{{
										statusFilter(
											[
												{ label: "保养", value: "BY" },
												{ label: "维修", value: "WX" },
												{ label: "定检", value: "DJ" },
												{ label: "巡查", value: "XC" },
											],
											form.categoryId
										)
									}}
								</el-form-item>
								<el-form-item label="状态">
									<el-tag
										v-if="form.status === 'complete'"
										color="#48C7A2"
										effect="dark"
										round
									>
										{{ searchStatus(form.status) }}
									</el-tag>
									<el-tag
										v-if="form.status === 'closed'"
										color="#FF636F"
										effect="dark"
										round
									>
										{{ searchStatus(form.status) }}
									</el-tag>
									<el-tag
										v-if="form.status === 'progress'"
										color="#F0AD57"
										effect="dark"
										round
									>
										{{ searchStatus(form.status) }}
									</el-tag>
								</el-form-item>
							</div>
						</div>
						<el-form-item label="计划人" v-if="form.planBy">
							{{ form.planBy }}
						</el-form-item>
						<el-form-item label="执行人" v-if="form.executeBy">
							{{ form.executeBy }}
						</el-form-item>
						<el-form-item label="负责人" v-if="form.headBy">
							{{ form.headBy }}
						</el-form-item>

						<el-form-item label="开始时间">
							{{ form.planStartAt }}
						</el-form-item>
						<el-form-item label="结束时间">
							{{ form.planEndAt }}
						</el-form-item>
						<el-form-item
							label="完成时间"
							v-if="form.status === 'complete'"
						>
							{{ form.overAt }}
						</el-form-item>
					</el-form>
				</div>
			</template>
		</el-drawer>
	</div>
</template>

<script>
import uploadConfig from "@/config/upload";
import commonEnum from "@/api/moudles/dm/common";

export default {
	name: "deviceInfoDetail",
	data() {
		return {
			visible: false,
			dealShow: false,
			title: null,
			form: {
				id: null,
				overAt: null,
			},
			drawerTitle: require("@/assets/images/drawer/drawer_icon_title.png"),
		};
	},
	methods: {
		open() {
			this.visible = true;
			return this;
		},
		getPage() {
			this.$emit("closed");
			this.$emit("success");
		},
		setData(data) {
			Object.assign(this.form, data);
			this.title = "运维计划详情";
		},
		statusFilter(key, v) {
			return commonEnum.searchKeys(key, v);
		},
		searchStatus(status) {
			return commonEnum.searchKeys(
				[
					{ label: "已完成", value: "complete" },
					{ label: "进行中", value: "progress" },
					{ label: "已关闭", value: "closed" },
				],
				status
			);
		},
		cancel() {},
		solutionSrc(fileId) {
			return uploadConfig.solutionImageSrc(fileId);
		},
		closed() {
			this.visible = false;
		},
	},
};
</script>

<style lang="scss" scoped>
.el-form-item__label {
	&::before {
		border-radius: 100%;
		width: 4px;
		height: 4px;
		content: "";
		background-color: #009fa8;
		display: inline-block;
		vertical-align: middle;
		position: inherit;
		left: -8px;
		top: 50%;
	}
}

.el-form-item {
	margin-bottom: 10px;
	font-size: 14px;
	line-height: 0;
}
</style>
