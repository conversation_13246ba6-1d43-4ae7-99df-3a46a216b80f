<template>
	<div class="drawer">
		<el-drawer
			v-model="visible"
			:size="640"
			custom-class="detail-drawer"
			destroy-on-close
			@closed="$emit('closed')"
		>
			<template #header>
				<div class="title">
					<div class="line"></div>
					<span>{{ title }}</span>
				</div>
			</template>
			<template #default>
				<div class="content">
					<wp-form
						ref="form"
						v-model="form"
						:config="config"
						:loading="loading"
					></wp-form>
				</div>
			</template>
			<template #footer>
				<div class="drawer-footer">
					<div class="drawer-button">
						<el-button @click="closed">取消</el-button>
						<el-button type="primary" @click="handleSubmit()"
							>提交</el-button
						>
					</div>
				</div>
			</template>
		</el-drawer>
	</div>
</template>

<script>
import { editConfig } from "./config";

export default {
	name: "DeviceInfoEdit",
	data() {
		const id = this.$route.query.id;
		return {
			visible: false,
			config: null,
			mode: "add",
			title: "新增",
			form: {
				id: id,
				code: null,
				name: null,
				type: null,
				abnormalType: null,
				common: "",
				status: "ENABLED",
				setValue: "",
				lastDiffValue: null,
			},
			loading: false,
			categoryId: "",
		};
	},

	created() {},
	methods: {
		open(data) {
			if (data) {
				if (data.pId !== "*" && data.categoryId) {
					this.form.categoryName = data.categoryName;
					this.form.categoryId = data.categoryId;
				}
				if (data.buildId) {
					this.form.buildName = data.buildName;
					this.form.buildId = data.buildId;
				}
			}
			this.visible = true;
			this.config = editConfig.bind(this)();
			return this;
		},

		setData(data) {
			let params = this.$TOOL.ObjFilter(this.form, data);
			this.title = "编辑";
			Object.assign(this.form, params);
		},

		handleSubmit() {
			this.$refs.form.validate(async (valid) => {
				if (!valid) {
					return;
				}

				let res;
				if (this.form.id) {
					res = await this.$API["dt/rule"].edit.post(this.form);
				} else {
					res = await this.$API["dt/rule"].add.post(this.form);
				}

				if (res.success) {
					this.$message.success(res.msg || "新增成功");
					this.$emit("getPage");
					this.closed();
				} else {
					this.$message.error(res.msg || "操作失败");
				}
				return res;
			});
		},

		closed() {
			this.$refs.form.resetFields();
			this.visible = false;
			this.$emit("closed");
		},
	},
};
</script>
<style lang="scss" scoped>
.content {
	padding: 20px;
}
</style>
