import { h, resolveComponent } from "vue";
import { CTX } from "@/api/config";

// 搜索model
export const listQuery = {
	name: "",
	key: "",
	categoryName: "",
	version: "",
	createdAt: "",
};

// 搜索表单配置
export const searchConfig = function () {
	return {
		list: [
			{
				type: "input",
				label: "流程名称",
				model: "processName",
				options: {},
			},
			{
				type: "input",
				label: "任务名称",
				model: "taskName",
				options: {},
			},
		],
		config: {
			size: "default",
			onSearch: () => {
				this.handleSubmit();
			},
			model: this.listQuery,
		},
	};
};
// 表格展示配置
export const tableColumns = function () {
	return [
		{
			type: "checkbox",
			fixed: "center",
		},
		{
			prop: "taskName",
			label: "任务名称",
			width: 180,
		},
		{
			prop: "processInstanceId",
			label: "流程实例编号",
			width: 120,
		},
		{
			prop: "executionId",
			label: "执行示例编号",
		},
		{
			prop: "businessKey",
			label: "业务号",
		},
		{
			prop: "processName",
			label: "流程名称",
		},
		{
			prop: "starter",
			label: "发起人",
		},
		{
			prop: "assignee",
			label: "办理人",
		},
		{
			prop: "createTime",
			label: "任务创建时间",
		},
		{
			prop: "startTime",
			label: "流程启动时间",
		},
		{
			prop: "opt",
			fixed: "right",
			label: "操作",
			align: "center",
			width: 150,
			render: (row) => {
				const optArr = [];
				const dealBtn = h(resolveComponent("wp-tip-button"), {
					content: "办理",
					type: "text",
					icon: "el-icon-position",
					onClick: () => {
						this.handleOpt("deal", row);
					},
				});

				optArr.push(dealBtn);
				return h("div", null, optArr);
			},
		},
	];
};

export const formConfig = function () {
	return {
		labelWidth: "130px",
		labelPosition: "right",
		size: "medium",
		formItems: [
			{
				label: "模型标识",
				name: "key",
				value: "",
				component: "input",
				showWordLimit: true,
				options: {
					maxlength: 50,
				},
				rules: [
					{
						required: true,
						message: "请输入模型标识",
						trigger: "blur",
					},
				],
			},
			{
				label: "模型名称",
				name: "name",
				value: "",
				component: "input",
				showWordLimit: true,
				options: {
					maxlength: 50,
				},
				rules: [
					{
						required: true,
						message: "请输入模型名称",
						trigger: "blur",
					},
				],
			},
			{
				label: "分类",
				name: "category",
				value: "",
				component: "select",
				options: {
					remote: {
						api: `${CTX.DM}category/getEnabledCategoryTree/AC`,
						data: {},
						props: {
							value: "id",
							label: "name",
						},
					},
					placeholder: "请选择分类",
				},
			},
			{
				label: "描述",
				name: "description",
				value: "",
				component: "textarea",
				options: {
					placeholder: "请输入描述",
					maxlength: 1000,
				},
			},
		],
	};
};
