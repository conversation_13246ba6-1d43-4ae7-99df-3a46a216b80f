<template>
	<el-container>
		<el-header>
			<div class="left-panel">
				<div class="left-panel-search">
					<wp-search-form :data="searchConfig"></wp-search-form>
				</div>
			</div>
		</el-header>
		<el-main class="nopadding">
			<wpTable
				ref="table"
				:apiObj="apiObj"
				:column="columns"
				row-key="id"
				stripe
				@selection-change="handleSelectionChange"
			>
				<template #finishStatus="scope">
					<el-tag
						v-if="scope.row.finishStatus === 'FINISHED'"
						type="success"
						effect="dark"
						round
					>
						{{ searchStatus(scope.row.finishStatus) }}
					</el-tag>
					<el-tag
						v-if="scope.row.finishStatus === 'OVERTIME'"
						type="danger"
						effect="dark"
						round
					>
						{{ searchStatus(scope.row.finishStatus) }}
					</el-tag>
					<el-tag
						v-if="scope.row.finishStatus === 'FINISHING'"
						type="warning"
						effect="dark"
						round
					>
						{{ searchStatus(scope.row.finishStatus) }}
					</el-tag>
				</template>
			</wpTable>
		</el-main>
		<detail-modal
			v-if="showDetail"
			ref="DetailModal"
			@upData="upData"
			@closed="showDetail = !showDetail"
		></detail-modal>
		<ht-dialog
			v-if="showDialog"
			ref="HtDialog"
			@closed="showDialog = !showDialog"
			@success="handleSubmit"
		></ht-dialog>
		<exception-form
			v-if="visible"
			ref="ExceptionForm"
			@closed="visible = !visible"
			@success="handleSubmit"
		></exception-form>
	</el-container>
</template>

<script>
import { tableColumns, listQuery, searchConfig } from "./config";
import DetailModal from "./detail";
import HtDialog from "./dialog";
import ExceptionForm from "./exceptionForm";
import commonEnum from "@/api/moudles/dm/common";

export default {
	components: {
		DetailModal,
		HtDialog,
		ExceptionForm,
	},
	name: "approveIndex",
	data() {
		return {
			apiObj: this.$API["dm/deviceReceive"].endPage,
			columns: tableColumns.bind(this)(),
			searchConfig: null,
			listQuery: Object.assign({}, listQuery),
			selections: [],
			visible: false,
			showDetail: false,
			dialogVisible: false,
			showDialog: false,
		};
	},
	methods: {
		upData() {
			this.$refs.table.upData();
		},

		handleSubmit() {
			this.$refs.table.upData(this.listQuery);
		},
		handleSelectionChange(val) {
			this.selections = val;
		},
		async handleOpt(type, row) {
			let result = null;
			row.dialogType = type;
			switch (type) {
				case "detail":
					this.showDetail = true;
					result = await this.$API[
						"dm/deviceReceive"
					].getWithDetail.get(row.id);
					this.$nextTick(() => {
						if (result.success) {
							this.$refs.DetailModal.open().setData(
								result.data,
								"index"
							);
						}
					});
					break;
				case "end":
					row.content = "结束使用";
					this.showDialog = true;
					this.$nextTick(() => {
						this.$refs.HtDialog.open(row);
					});
					break;
				case "abnormal":
					row.content = "异常报告";
					this.visible = true;
					this.$nextTick(() => {
						this.$refs.ExceptionForm.openPage(row);
					});
					break;
				default:
			}
		},
		searchStatus(finishStatus) {
			return commonEnum.searchKeys(commonEnum.endUseStatus, finishStatus);
		},
	},
	computed: {},
	watch: {},
	created() {
		this.searchConfig = searchConfig.bind(this)();
	},
	mounted() {},
};
</script>

<style scoped></style>
