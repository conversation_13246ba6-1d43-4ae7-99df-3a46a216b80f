import { h, resolveComponent } from "vue";
import commonEnum from "@/api/moudles/dm/common";

// 搜索model
export const listQuery = {
	name: "",
	code: "",
	barCode: "",
	buildId: "",
	specs: "",
	produceName: "",
	supplierName: "",
	countriesArea: "",
	applyType: "",
	controlMode: "",
	belongOrgId: "",
};

// 搜索表单配置
export const searchConfig = function () {
	return {
		list: [
			{
				type: "datetimerange",
				label: "审批时间",
				model: "approveAt",
				options: {
					"format":"YYYY-MM-DD HH:mm:ss"
				},
			},
			{
				type: "input",
				label: "名称搜索",
				model: "deviceName",
				options: {
					placeholder: "请输入仪器设备名称",
				},
			},

			{
				type: "input",
				label: "规格型号",
				model: "specs",
				options: {},
			},
			{
				type: "select",
				label: "状态",
				model: "status",
				options: {
					options: commonEnum.deviceUseApproveStatus,
				},
			},
		],
		config: {
			size: "default",
			onSearch: () => {
				this.handleSubmit();
			},
			model: this.listQuery,
		},
	};
};
// 表格展示配置
export const tableColumns = function () {
	return [
		{
			type: "checkbox",
			fixed: "center",
		},

		/*		{
			prop: 'fileId', label: '图片', width: 120
		},*/
		{
			prop: "deviceName",
			label: "名称",
			width: 150,
		},
		{
			prop: "categoryName",
			label: "类型",
			width: 150,
		},
		{
			prop: "specs",
			label: "规格型号",
			width: 150,
		},
		{
			prop: "createdByName",
			label: "申请/领用人",
			width: 150,
		},
		{
			prop: "applyTypeName",
			label: "申请类型",
			width: 150,
		},
		{
			prop: "purposeTypeName",
			label: "申请用途",
			width: 120,
		},
		{
			prop: "approveByName",
			label: "审批人",
			width: 120,
		},
		{
			prop: "approveAt",
			label: "审批时间",
			width: 200,
		},
		{
			prop: "status",
			label: "状态",
		},
		{
			prop: "opt",
			fixed: "right",
			label: "操作",
			align: "center",
			width: 160,
			render: (row) => {
				const optArr = [];
				const detailBtn = h(resolveComponent("wp-tip-button"), {
					link: true,
					content: "申请详情",
					type: "text",
					icon: "wp-icon-detail",
					onClick: () => {
						this.handleOpt("detail", row);
					},
				});

				const passBtn = h(resolveComponent("wp-tip-button"), {
					link: true,
					content: "通过申请",
					type: "text",
					icon: "wp-icon-pass",
					onClick: () => {
						this.handleOpt("pass", row);
					},
				});

				const rejectedBtn = h(resolveComponent("wp-tip-button"), {
					link: true,
					content: "驳回申请",
					type: "text",
					icon: "wp-icon-reject",
					onClick: () => {
						this.handleOpt("rejected", row);
					},
				});

				optArr.push(detailBtn);
				console.log("row", row);
				console.log(
					"new Date() <= Date.parse(row.endAt)",
					new Date() <= Date.parse(row.endAt)
				);
				if (
					row.status === "APPLIED" &&
					new Date() <= Date.parse(row.endAt)
				) {
					optArr.push(passBtn);
					optArr.push(rejectedBtn);
				}

				return h("div", null, optArr);
			},
		},
	];
};
