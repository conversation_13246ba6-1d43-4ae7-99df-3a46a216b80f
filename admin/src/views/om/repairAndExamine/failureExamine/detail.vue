<template>
	<div class="drawer detail">
		<el-drawer
			v-model="visible"
			:size="640"
			custom-class="detail-drawer"
			destroy-on-close
			@closed="$emit('closed')"
		>
			<template #header>
				<div class="title">
					<div class="line"></div>
					<span>{{ title }}</span>
				</div>
			</template>
			<template #default>
				<div class="content">
					<el-form
						ref="form"
						:model="form"
						class="form-item"
						label-position="left"
						label-width="80px"
					>
						<div>
							<div class="drawer-item">
								<div class="drawer-item-title">
									<img :src="drawerTitle" alt="基础信息" />
									<span>基础信息</span>
								</div>
							</div>
							<el-row :gutter="18" class="mt10">
								<el-col
									:span="12"
									v-if="form.status === 'UNDO'"
								>
									<el-form-item
										label="状态"
										style="height: 32px"
									>
										<el-tag
											v-if="form.status === 'UNDO'"
											color="#F0AD57"
											effect="dark"
											round
										>
											未检修
										</el-tag>
										<el-tag
											v-if="form.status === 'DONE'"
											color="#48C7A2"
											effect="dark"
											round
										>
											已检修
										</el-tag>
									</el-form-item>
								</el-col>
								<el-col
									:span="form.status === 'UNDO' ? 12 : 24"
								>
									<el-form-item label="设备名称">
										{{ form.deviceName }}
									</el-form-item>
								</el-col>
								<el-col :span="12">
									<el-form-item label="作业部门">
										{{ form.examineOrgName }}
									</el-form-item>
								</el-col>
								<el-col :span="12">
									<el-form-item label="登记人">
										{{ form.checkByName }}
									</el-form-item>
								</el-col>
								<el-col :span="12">
									<el-form-item label="登记时间">
										{{ form.checkAt }}
									</el-form-item>
								</el-col>
								<el-col :span="24">
									<el-form-item label="故障概述">
										{{ form.result }}
									</el-form-item>
								</el-col>

								<el-col :span="24">
									<el-form-item label="故障说明">
										{{ form.resultDetail }}
									</el-form-item>
								</el-col>
								<el-col :span="24">
									<el-form-item
										label="相关图片"
										style="height: auto"
									>
										<ul
											class="el-upload-list image"
											style="margin-top: 10px"
										>
											<li
												v-for="item in form.djFileRelList"
												:key="item.fileId"
												class="el-upload-list__item"
											>
												<el-image
													:preview-src-list="[
														solutionSrc(
															item.fileId
														),
													]"
													:src="
														solutionSrc(item.fileId)
													"
													alt="点击查看大图"
													preview-teleported
													style="
														width: 120px;
														height: 120px;
													"
													title="点击查看大图"
												>
													<template #error>
														<el-image
															:src="$imageDefault"
															style="
																width: 120px;
																height: 120px;
															"
														></el-image>
													</template>
												</el-image>
											</li>
										</ul>
									</el-form-item>
								</el-col>
							</el-row>
						</div>
						<div v-if="form.status === 'DONE'">
							<div class="drawer-item">
								<div class="drawer-item-title">
									<img :src="drawerTitle" alt="处理信息" />
									<span>处理信息</span>
								</div>
							</div>
							<el-row :gutter="18" class="mt10">
								<el-col :span="24">
									<el-form-item label="处理结果">
										<span style="color: #009fa8">
											已检修
										</span>
									</el-form-item>
								</el-col>
								<el-col :span="12">
									<el-form-item label="检修人员">
										{{ form.examineByName }}
									</el-form-item>
								</el-col>
								<el-col :span="12">
									<el-form-item label="维修时间">
										{{ form.examineAt }}
									</el-form-item>
								</el-col>

								<el-col :span="24">
									<el-form-item label="检修说明">
										{{ form.examineResult }}
									</el-form-item>
								</el-col>
								<el-col :span="24">
									<el-form-item
										label="检修图片"
										style="height: auto"
									>
										<ul
											class="el-upload-list image"
											style="margin-top: 10px"
										>
											<li
												v-for="item in form.jxFileRelList"
												:key="item.fileId"
												class="el-upload-list__item"
											>
												<el-image
													:preview-src-list="[
														solutionSrc(
															item.fileId
														),
													]"
													:src="
														solutionSrc(item.fileId)
													"
													alt="点击查看大图"
													preview-teleported
													style="
														width: 120px;
														height: 120px;
													"
													title="点击查看大图"
												>
													<template #error>
														<el-image
															:src="$imageDefault"
															style="
																width: 120px;
																height: 120px;
															"
														></el-image>
													</template>
												</el-image>
											</li>
										</ul>
									</el-form-item>
								</el-col>
							</el-row>
						</div>
					</el-form>
				</div>
			</template>
			<template #footer>
				<div class="drawer-footer" v-if="form.status === 'UNDO'">
					<div class="drawer-button">
						<el-button type="primary" @click="dealRepair"
							>处理检修</el-button
						>
					</div>
				</div>
			</template>
		</el-drawer>
		<deal-modal
			v-if="dealShow"
			ref="dealModal"
			@closed="dealShow = !dealShow"
			@success="getPage"
		></deal-modal>
	</div>
</template>

<script>
import dealModal from "./deal";
import uploadConfig from "@/config/upload";

export default {
	name: "deviceInfoDetail",
	components: {
		dealModal,
	},
	data() {
		return {
			visible: false,
			dealShow: false,
			title: null,
			form: {
				id: null,
				deviceName: null,
				deviceId: null,
				examineByName: null,
				examineAt: null,
				examineResult: null,
				djFileRelList: null,
				jxFileRelList: null,
			},
			drawerTitle: require("@/assets/images/drawer/drawer_icon_title.png"),
		};
	},
	methods: {
		open() {
			this.visible = true;
			return this;
		},
		getPage() {
			this.$emit("closed");
			this.$emit("success");
		},
		setData(data) {
			Object.assign(this.form, data);
			this.title = "检修详情";
		},
		async dealRepair() {
			this.dealShow = true;
			this.$nextTick(() => {
				this.$refs.dealModal.open(this.form.id);
			});
		},
		cancel() {},
		solutionSrc(fileId) {
			return uploadConfig.solutionSrc(fileId);
		},
		closed() {
			this.visible = false;
		},
	},
};
</script>

<style lang="scss" scoped>
.el-form-item__label {
	&::before {
		border-radius: 100%;
		width: 4px;
		height: 4px;
		content: "";
		background-color: #009fa8;
		display: inline-block;
		vertical-align: middle;
		position: inherit;
		left: -8px;
		top: 50%;
	}
}

.el-form-item {
	margin-bottom: 10px;
	font-size: 14px;
	line-height: 0;
}
</style>
