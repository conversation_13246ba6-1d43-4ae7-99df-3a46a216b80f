<template>
	<div class="drawer">
		<el-drawer
			v-model="visible"
			:size="640"
			custom-class="detail-drawer"
			destroy-on-close
			@closed="$emit('closed')"
		>
			<template #header>
				<div class="title">
					<div class="line"></div>
					<span>{{ title }}</span>
				</div>
			</template>
			<template #default>
				<div class="content">
					<wp-form
						ref="form"
						v-model="form"
						:config="config"
						:loading="loading"
					></wp-form>
				</div>
			</template>
			<template #footer>
				<div class="drawer-footer">
					<el-button
						v-if="form.repairId"
						type="text"
						style="float: left; font-size: 12px"
						@click="viewQueue"
						>查看关联的报修登记
					</el-button>
					<div class="drawer-button">
						<el-button @click="closed">取消</el-button>
						<el-button type="primary" @click="handleSubmit()"
							>提交</el-button
						>
					</div>
				</div>
			</template>
		</el-drawer>
		<repair-model
			v-if="showDetail"
			ref="dealModal"
			@closed="showDetail = !showDetail"
		></repair-model>
	</div>
</template>

<script>
import { editConfig } from "./config";
import repairModel from "./repairDetail";
export default {
	name: "FailureExamineEdit",
	components: {
		repairModel,
	},
	data() {
		const id = this.$route.query.id;
		return {
			visible: false,
			config: null,
			mode: "add",
			title: "新增",
			showDetail: false,
			form: {
				id: id,
				repairId: null,
				code: null,
				status: "UNDO",
				result: null,
				resultDetail: null,
				categoryId: null,
				categoryName: null,
				repairBy: null,
				repairOrgId: null,
				checkAt: null,
				checkBy: null,
				deviceId: null,
				examineAt: null,
				examineOrgId: null,
				examineOrgName: null,
				examineBy: null,
				fileRelList: null,
			},
			loading: false,
		};
	},

	created() {},
	methods: {
		open() {
			this.visible = true;
			this.config = editConfig.bind(this)();
			return this;
		},

		async viewQueue() {
			this.showDetail = true;
			let result = await this.$API["om/failureRepair"].failureRepair.get(
				this.form.repairId
			);
			this.$nextTick(() => {
				if (result.success) {
					this.$refs.dealModal.open().setData(result.data);
				}
			});
		},

		setData(data) {
			let params = this.$TOOL.ObjFilter(this.form, data);
			this.title = "编辑";
			Object.assign(this.form, params);
			this.form.fileRelList = data.djFileRelList;
		},

		handleSubmit() {
			this.$refs.form.validate(async (valid) => {
				if (!valid) {
					return;
				}

				let res;
				if (this.form.id) {
					res = await this.$API["om/failureExamine"].edit.post(
						this.form
					);
				} else {
					res = await this.$API["om/failureExamine"].add.post(
						this.form
					);
				}

				if (res.success) {
					this.$message.success(res.msg || "新增成功");
					this.$emit("success");
					this.closed();
				} else {
					this.$message.error(res.msg || "操作失败");
				}
				return res;
			});
		},

		closed() {
			this.$refs.form.resetFields();
			this.visible = false;
			this.$emit("closed");
		},
	},
};
</script>
<style lang="scss" scoped>
.content {
	padding: 20px;
}
</style>
