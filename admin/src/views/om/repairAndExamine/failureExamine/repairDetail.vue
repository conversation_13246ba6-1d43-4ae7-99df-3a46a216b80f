<template>
	<div class="drawer detail">
		<wp-dialog
			v-model="visible"
			:showFullscreen="false"
			width="640px"
			draggable
			custom-class="detail-drawer"
			title="关联的报修详情"
		>
			<div class="content">
				<el-form
					ref="form"
					:model="form"
					class="form-item"
					label-position="left"
					label-width="80px"
				>
					<div>
						<el-row :gutter="18" class="mt10">
							<el-col :span="12" v-if="form.status === 'UNDO'">
								<el-form-item
									label="任务状态"
									style="height: 32px"
								>
									<el-tag
										v-if="form.status === 'UNDO'"
										color="#F0AD57"
										effect="dark"
										round
									>
										未处理
									</el-tag>
									<el-tag
										v-if="form.status === 'DONE'"
										color="#48C7A2"
										effect="dark"
										round
									>
										已处理
									</el-tag>
								</el-form-item>
							</el-col>
							<el-col :span="form.status === 'UNDO' ? 12 : 24">
								<el-form-item label="设备名称">
									{{ form.categoryName }}
								</el-form-item>
							</el-col>

							<el-col :span="12">
								<el-form-item label="报修人员">
									{{ form.reportByName }}
								</el-form-item>
							</el-col>
							<el-col :span="12">
								<el-form-item label="报修时间">
									{{ form.reportAt }}
								</el-form-item>
							</el-col>
							<el-col :span="24">
								<el-form-item label="故障概述">
									{{ form.result }}
								</el-form-item>
							</el-col>

							<el-col :span="24">
								<el-form-item label="报修说明">
									{{ form.resultDetail }}
								</el-form-item>
							</el-col>
							<el-col :span="24">
								<el-form-item
									label="相关图片"
									style="height: auto"
								>
									<ul
										class="el-upload-list image"
										style="margin-top: 10px"
									>
										<li
											v-for="item in form.fileRelList"
											:key="item.fileId"
											class="el-upload-list__item"
										>
											<el-image
												:preview-src-list="[
													solutionSrc(item.fileId),
												]"
												:src="solutionSrc(item.fileId)"
												alt="点击查看大图"
												preview-teleported
												style="
													width: 120px;
													height: 120px;
												"
												title="点击查看大图"
											>
												<template #error>
													<el-image
														:src="$imageDefault"
														style="
															width: 120px;
															height: 120px;
														"
													></el-image>
												</template>
											</el-image>
										</li>
									</ul>
								</el-form-item>
							</el-col>
						</el-row>
					</div>
				</el-form>
			</div>
		</wp-dialog>
	</div>
</template>

<script>
import uploadConfig from "@/config/upload";

export default {
	name: "repairDetail",
	components: {},
	data() {
		return {
			dealTypeList: {
				EXAMINE: "安排检修",
				WARN_REG: "报警登记",
				CLOSED: "直接关闭",
			},
			visible: false,
			title: null,
			form: {
				id: null,
			},
			drawerTitle: require("@/assets/images/drawer/drawer_icon_title.png"),
		};
	},
	methods: {
		open() {
			this.visible = true;
			return this;
		},
		getPage() {
			this.$emit("closed");
		},
		setData(data) {
			Object.assign(this.form, data);
			this.title = "报修详情";
		},
		cancel() {},
		solutionSrc(fileId) {
			return uploadConfig.solutionSrc(fileId);
		},
		closed() {
			this.visible = false;
		},
	},
};
</script>

<style lang="scss" scoped></style>
