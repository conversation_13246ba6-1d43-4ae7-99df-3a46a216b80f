<template>
	<el-container>
		<!-- Left Aside for Tree -->
		<el-aside width="auto">
			<el-container>
				<el-header style="padding-top: 10px">
						<el-input
							v-model.trim="filterText"
							clearable
							placeholder="输入关键字过滤"
						></el-input>
				</el-header>
				<el-main class="nopadding">
					<el-tree
						ref="categoryTreeRef"
						:data="categoryTreeData"
						:props="defaultProps"
						:filter-node-method="filterNode"
						node-key="id"
						:expand-on-click-node="false"
						default-expand-all
						:highlight-current="true"
						class="menu"
						@node-click="handleNodeClick"
					>
						<template #default="{ node, data }">
							<span class="custom-tree-node">
								<span>{{ node.label }}</span>
							</span>
						</template>
					</el-tree>
				</el-main>
			</el-container>
		</el-aside>

		<!-- Right Side: Main Content -->
		<el-container>
			<el-header style="margin-top: 0">
				<div class="left-panel">
					<div class="left-panel-search">
						<wp-search-form :data="searchConfig"></wp-search-form>
					</div>
				</div>
			</el-header>
			<el-header style="padding-top: 13px">
				<el-button type="primary" @click="add">检修登记</el-button>
			</el-header>
			<el-main class="nopadding">
				<wpTable
					ref="table"
					:apiObj="apiObj"
					:column="columns"
					row-key="id"
					stripe
					@selection-change="handleSelectionChange"
				>
					<template #status="scope">
						<el-tag
							v-if="scope.row.status === 'UNDO'"
							color="#F0AD57"
							effect="dark"
							round
						>
							未检修
						</el-tag>
						<el-tag
							v-if="scope.row.status === 'DONE'"
							color="#48C7A2"
							effect="dark"
							round
						>
							已检修
						</el-tag>
					</template>
				</wpTable>
			</el-main>
		</el-container>

		<!-- Modals -->
		<edit-modal
			v-if="visible"
			ref="editModal"
			@closed="visible = !visible"
			@success="getPage"
		></edit-modal>
		<detail-modal
			v-if="showDetail"
			ref="DetailModal"
			@closed="showDetail = !showDetail"
			@success="handleSubmit"
		></detail-modal>
		<deal-modal
			v-if="dealShow"
			ref="dealModal"
			@closed="dealShow = !dealShow"
			@success="getPage"
		></deal-modal>
	</el-container>
</template>

<script>
import { tableColumns, listQuery, searchConfig } from "./config";
import editModal from "./edit";
import detailModal from "./detail";
import dealModal from "./deal";

export default {
	name: "FailureExamineTable",
	components: {
		editModal,
		detailModal,
		dealModal,
	},
	data() {
		return {
			apiObj: this.$API["om/failureExamine"].page,
			columns: tableColumns.bind(this)(),
			searchConfig: null,
			listQuery: Object.assign({}, listQuery),
			selections: [],
			visible: false,
			dealShow: false,
			showDetail: false,
			dialogVisible: false,
		};
	},
	computed: {},
	watch: {},
	created() {
		this.searchConfig = searchConfig.bind(this)();
	},
	mounted() {},
	methods: {
		getPage() {
			this.$refs.table.upData(this.listQuery);
		},
		handleSubmit() {
			this.$refs.table.upData(this.listQuery);
		},
		openDetail(row) {
			this.handleOpt("detail", row);
		},
		handleSelectionChange(val) {
			this.selections = val;
		},
		async handleOpt(type, row) {
			let result = null;
			switch (type) {
				case "detail":
					this.showDetail = true;
					result = await this.$API[
						"om/failureExamine"
					].failureExamine.get(row.id);
					this.$nextTick(() => {
						if (result.success) {
							this.$refs.DetailModal.open().setData(result.data);
						}
					});
					break;
				case "edit":
					this.visible = true;
					result = await this.$API[
						"om/failureExamine"
					].failureExamine.get(row.id);
					this.$nextTick(() => {
						this.$refs.editModal.open().setData(result.data);
					});
					break;
				case "deal":
					this.dealShow = true;
					result = await this.$API[
						"om/failureExamine"
					].failureExamine.get(row.id);
					this.$nextTick(() => {
						this.$refs.dealModal.open(result.data);
					});
					break;
				case "delete":
					this.$confirm("确认删除吗？", "提示", {
						confirmButtonText: "确定",
						cancelButtonText: "取消",
						type: "warning",
					})
						.then(() => {
							result = this.$API[
								"om/failureExamine"
							].batchDelete.delete([row.id]);
							this.$nextTick(() => {
								if (result.success) {
									this.$message.success(result.msg || "删除");
									this.getPage();
								}
							});
						})
						.catch(() => {
							this.$message.info("已取消删除");
						});

					break;
				default:
			}
		},
		add() {
			this.visible = true;
			this.$nextTick(() => {
				this.$refs.editModal.open();
			});
		},
	},
};
</script>
<style scoped></style>
