import { h, resolveComponent } from "vue";

//组件参数数据
export const componentsData = {
	status: [
		{ label: "未检修", value: "UNDO" },
		{ label: "已检修", value: "DONE" },
	],
};
// 搜索model
export const listQuery = {
	deviceId: null,
};

// 搜索表单配置
export const searchConfig = function () {
	return {
		list: [
			{
				type: "selectRemote",
				label: "设备名称",
				model: "deviceId",
				options: {
					request: this.$API['dt/device-info'].deviceInfoPage.get,
					items: [],
					props: {
						children: "children",
						label: "name",
					},
				},
			},
			{
				type: "input",
				label: "检修人",
				model: "keyName",
				options: {},
			},
			{
				type: "input",
				label: "故障概述",
				model: "result",
				options: {},
			},
			{
				type: "select",
				label: "状态",
				model: "status",
				options: {
					options: componentsData.status,
				},
			},
			{
				type: "datetimerange",
				label: "检修时间",
				model: "examineAt",
				options: {},
			},
		],
		config: {
			size: "default",
			onSearch: () => {
				this.handleSubmit();
			},
			model: this.listQuery,
		},
	};
};

// 表格展示配置
export const tableColumns = function () {
	return [
		{
			type: "checkbox",
			fixed: "center",
		},
		{
			label: "设备名称",
			prop: "deviceName",
			showOverflowTooltip: true,
			align: "left",
		},
		{
			label: "登记时间",
			prop: "checkAt",
			align: "left",
			showOverflowTooltip: true,
		},
		{
			label: "登记人",
			prop: "checkByName",
			showOverflowTooltip: true,
			align: "left",
		},
		{
			label: "检修时间",
			prop: "examineAt",
			align: "left",
			showOverflowTooltip: true,
			// render: (row) => {
			// 	const examineAt = row.examineAt;
			// 	if (examineAt) {
			// 		return examineAt.substr(0, 10);
			// 	}
			// 	return '';
			// }
		},
		{
			label: "检修人",
			prop: "examineByName",
			showOverflowTooltip: true,
			align: "left",
		},
		{
			label: "故障概述",
			prop: "result",
			showOverflowTooltip: true,
			align: "left",
		},
		{
			label: "故障说明",
			prop: "resultDetail",
			showOverflowTooltip: true,
			align: "left",
		},
		{
			label: "状态",
			prop: "status",
			showOverflowTooltip: true,
			align: "left",
		},
		{
			prop: "opt",
			fixed: "right",
			label: "操作",
			align: "center",
			render: (row) => {
				const optArr = [];
				const detailBtn = h(resolveComponent("wp-tip-button"), {
					content: "详情",
					type: "text",
					icon: "wp-icon-detail",
					onClick: () => {
						this.handleOpt("detail", row);
					},
				});

				const editBtn = h(resolveComponent("wp-tip-button"), {
					content: "编辑",
					type: "text",
					icon: "wp-icon-edit",
					onClick: () => {
						// 编辑
						this.handleOpt("edit", row);
					},
				});

				const dealBtn = h(resolveComponent("wp-tip-button"), {
					content: "处理检修",
					type: "text",
					icon: "wp-icon-add-record",
					onClick: () => {
						this.handleOpt("deal", row);
					},
				});

				const delBtn = h(resolveComponent("wp-tip-button"), {
					content: "删除",
					type: "text",
					icon: "wp-icon-delete",
					onClick: () => {
						// 编辑
						this.handleOpt("delete", row);
					},
				});

				optArr.push(detailBtn);
				if (row.status === "UNDO") {
					optArr.push(editBtn);
					optArr.push(dealBtn);
				}

				optArr.push(delBtn);
				return h("div", null, optArr);
			},
		},
	];
};

// 表格展示配置
export const editConfig = function () {
	return {
		labelWidth: "130px",
		labelPosition: "top",
		size: "medium",
		formItems: [
			{
				label: "设备名称",
				name: "deviceId",
				value: "",
				component: "selectRemote",
				span: 12,
				options: {
					request: this.$API['dt/device-info'].deviceInfoPage.get,
				},
				rules: [
					{
						required: true,
						message: "请选择设备名称",
						trigger: "blur",
					},
				],
			},
			{
				label: "登记时间",
				name: "checkAt",
				value: "",
				component: "date",
				span: 12,
				options: {
					type: "datetime",
					format: "YYYY-MM-DD HH:mm:ss",
				},
				rules: [
					{
						required: true,
						message: "请选择登记时间",
						trigger: "blur",
					},
				],
			},
			{
				label: "登记人",
				name: "checkBy",
				value: "",
				component: "selectRemote",
				span: 12,
				options: {
					queryKey: "keyword",
					request: this.$API["uc/user"].pageSimple.get,
					params: { pageSize: 500, status: "ENABLED" },
					placeholder: "搜索查询",
				},
				rules: [
					{
						required: true,
						message: "请选择登记人",
						trigger: "blur",
					},
				],
			},
			{
				label: "作业部门",
				name: "examineOrgId",
				defaultText: "examineOrgName",
				component: "inputTree",
				span: 12,
				options: {
					request: this.$API["uc/org"].getOrgTree.get,
					items: [],
				},
				rules: [
					{
						required: true,
						message: "请选择作业部门",
						trigger: "blur",
					},
				],
			},
			{
				label: "故障概述",
				name: "result",
				component: "input",
				span: 24,
				options: {},
				rules: [{ required: true, message: "必填项", trigger: "blur" }],
			},
			{
				label: "故障说明",
				name: "resultDetail",
				value: "",
				component: "textarea",
				span: 24,
				options: {
					maxlength: "500",
					placeholder: "请输入检定备注",
				},
			},
			{
				label: "上传图片",
				component: "upload",
				name: "fileRelList",
				value: [],
				options: {
					multiple: true,
				},
				rules: [{ required: true, message: "必填项", trigger: "blur" }],
			},
		],
	};
};
