import { h, resolveComponent } from "vue";

//组件参数数据
export const componentsData = {
	status: [
		{ label: "未处理", value: "UNDO" },
		{ label: "已处理", value: "DONE" },
	],
};
// 搜索model
export const listQuery = {
	deviceId: null,
};

// 搜索表单配置
export const searchConfig = function () {
	return {
		list: [
			// {
			// 	type: "selectRemote",
			// 	label: "设备名称",
			// 	model: "deviceId",
			// 	options: {
			// 		request: this.$API['dt/device-info'].deviceInfoPage.get,
			// 		items: [],
			// 		props: {
			// 			children: "children",
			// 			label: "name",
			// 		},
			// 	},
			// },
			{
				type: "input",
				label: "报修人",
				model: "keyName",
				options: {},
			},
			{
				type: "select",
				label: "状态",
				model: "status",
				options: {
					options: componentsData.status,
				},
			},
			{
				type: "datetimerange",
				label: "报修时间",
				model: "reportAt",
				options: {},
			},
		],
		config: {
			size: "default",
			onSearch: () => {
				this.handleSubmit();
			},
			model: this.listQuery,
		},
	};
};

// 表格展示配置
export const tableColumns = function () {
	return [
		{
			type: "checkbox",
			fixed: "center",
		},
		{
			label: "设备名称",
			prop: "deviceName",
			showOverflowTooltip: true,
			align: "left",
		},

		{
			label: "报修时间",
			prop: "reportAt",
			align: "left",
			showOverflowTooltip: true,
		},
		{
			label: "报修人",
			prop: "reportByName",
			showOverflowTooltip: true,
			align: "left",
		},
		{
			label: "报修概述",
			prop: "result",
			showOverflowTooltip: true,
			align: "left",
		},
		{
			label: "报修说明",
			prop: "resultDetail",
			showOverflowTooltip: true,
			align: "left",
		},

		{
			label: "状态",
			prop: "status",
			showOverflowTooltip: true,
			align: "left",
		},
		{
			prop: "opt",
			fixed: "right",
			label: "操作",
			align: "center",
			render: (row) => {
				const optArr = [];
				const detailBtn = h(resolveComponent("wp-tip-button"), {
					content: "详情",
					type: "text",
					icon: "wp-icon-detail",
					onClick: () => {
						this.handleOpt("detail", row);
					},
				});

				const editBtn = h(resolveComponent("wp-tip-button"), {
					content: "编辑",
					type: "text",
					icon: "wp-icon-edit",
					onClick: () => {
						// 编辑
						this.handleOpt("edit", row);
					},
				});

				const dealBtn = h(resolveComponent("wp-tip-button"), {
					content: "处理报修",
					type: "text",
					icon: "wp-icon-add-record",
					onClick: () => {
						this.handleOpt("deal", row);
					},
				});

				const delBtn = h(resolveComponent("wp-tip-button"), {
					content: "删除",
					type: "text",
					icon: "wp-icon-delete",
					onClick: () => {
						// 编辑
						this.handleOpt("delete", row);
					},
				});

				optArr.push(detailBtn);
				if (row.status === "UNDO") {
					optArr.push(editBtn);
					optArr.push(dealBtn);
				}
				optArr.push(delBtn);
				return h("div", null, optArr);
			},
		},
	];
};

// 表格展示配置
export const editConfig = function () {
	return {
		labelWidth: "130px",
		labelPosition: "top",
		size: "medium",
		formItems: [
			{
				label: "设备名称",
				name: "deviceId",
				defaultText: "deviceName",
				component: "selectRemote",
				span: 12,
				options: {
					request: this.$API['dt/device-info'].deviceInfoPage.get,
					items: [],
					props: {
						children: "children",
						label: "name",
					},
				},
				rules: [
					{
						required: true,
						message: "请选择设备名称",
						trigger: "blur",
					},
				],
			},
			{
				label: "报修时间",
				name: "reportAt",
				value: "",
				component: "date",
				span: 12,
				options: {
					type: "datetime",
					format: "YYYY-MM-DD HH:mm:ss",
				},
				rules: [
					{
						required: true,
						message: "请选择报修时间",
						trigger: "blur",
					},
				],
			},
			{
				label: "报修人",
				name: "reportBy",
				value: "",
				component: "selectRemote",
				span: 12,
				options: {
					queryKey: "keyword",
					request: this.$API["uc/user"].pageSimple.get,
					params: { pageSize: 50, status: "ENABLED" },
					placeholder: "搜索查询",
				},
				rules: [
					{
						required: true,
						message: "请选择报修人",
						trigger: "blur",
					},
				],
			},
			{
				label: "故障概述",
				name: "result",
				component: "input",
				span: 24,
				options: {},
				rules: [{ required: true, message: "必填项", trigger: "blur" }],
			},
			{
				label: "报修说明",
				name: "resultDetail",
				value: "",
				component: "textarea",
				span: 24,
				options: {
					maxlength: "500",
					placeholder: "请输入检定备注",
				},
			},
			{
				label: "上传图片",
				component: "upload",
				name: "fileRelList",
				value: [],
				options: {
					multiple: true,
				},
				rules: [{ required: true, message: "必填项", trigger: "blur" }],
			},
		],
	};
};
