<template>
	<div class="drawer detail" v-if="dialogVisible">
		<wp-dialog
			v-model="dialogVisible"
			:showFullscreen="false"
			width="580px"
			class="detail-drawer"
			draggable
			title="处理报修"
		>
			<div class="drawer-content">
				<el-form
					ref="ruleForm"
					:model="form"
					:rules="rules"
					class="form-item"
					label-position="top"
				>
					<el-row :gutter="20">
						<el-col :span="24">
							<el-form-item label="处理方式" prop="dealType">
								<el-radio-group v-model="form.dealType">
									<el-radio-button
										label="EXAMINE"
										style="border-radius: 0 0"
										>安排检修</el-radio-button
									>
<!--									<el-radio-button-->
<!--										label="WARN_REG"-->
<!--										style="-->
<!--											margin-left: 20px;-->
<!--											border-left: 1px solid #d7dae1;-->
<!--											border-radius: 0 0;-->
<!--										"-->
<!--									>-->
<!--										报警登记-->
<!--									</el-radio-button>-->
									<el-radio-button
										label="CLOSED"
										style="
											margin-left: 20px;
											border-left: 1px solid #d7dae1;
											border-radius: 0 0;
										"
									>
										直接关闭
									</el-radio-button>
								</el-radio-group>
							</el-form-item>
						</el-col>
						<el-col :span="12">
							<el-form-item
								label="处理人"
								prop="dealBy"
								style="margin-bottom: 15px"
							>
								<wp-select-remote
									v-model="form.dealBy"
									:multiple="false"
									:params="{
										status: 'ENABLED',
										pageSize: 1000,
									}"
									placeholder="请选择处理人"
									queryKey="keyword"
									:request="
										this.$API['uc/user'].pageSimple.get
									"
									style="width: 100%"
								>
								</wp-select-remote>
							</el-form-item>
						</el-col>
						<el-col :span="24">
							<el-form-item
								label="处理说明"
								prop="dealResult"
								style="margin-bottom: 15px"
							>
								<el-input
									v-model.trim="form.dealResult"
									:autosize="{ minRows: 4, maxRows: 6 }"
									maxlength="100"
									placeholder="请输入处理说明"
									show-word-limit
									type="textarea"
								/>
							</el-form-item>
						</el-col>
					</el-row>
				</el-form>
			</div>
			<template #footer>
				<el-button @click="closed">取 消</el-button>
				<el-button type="primary" @click="onSubmit">确 定</el-button>
			</template>
		</wp-dialog>
	</div>
</template>

<script>
export default {
	name: "deal",
	data() {
		return {
			dialogVisible: false, // 对话框 默认关闭
			dealTypeList: {
				EXAMINE: "安排检修",
				WARN_REG: "报警登记",
				CLOSED: "直接关闭",
			},
			isShow: true,
			form: {
				id: null,
				dealType: null,
				dealBy: null,
				dealResult: null,
				status: "DONE",
			},
			defaultTime: new Date(),
			rules: {
				dealType: [
					{
						required: true,
						message: "请选择报修处理方式",
						trigger: "blur",
					},
				],
				dealBy: [
					{
						required: true,
						message: "请选择处理人",
						trigger: "blur",
					},
				],
			},
		};
	},
	methods: {
		open(id) {
			this.dialogVisible = true;
			this.form.id = id;
			return this;
		},
		setData(data) {
			Object.assign(this.form, data);
		},

		async onSubmit() {
			await this.$refs["ruleForm"].validate(async (valid) => {
				if (valid) {
					let result = await this.$API[
						"om/failureRepair"
					].dealRepair.post(this.form);
					if (result.success) {
						this.$message.success(result.msg || "操作成功");
						this.$emit("success");
						this.closed();
					} else {
						this.$message.error("操作失败");
					}
				}
			});
		},
		reset() {
			this.$refs["ruleForm"].resetFields();
		},
		closed() {
			this.dialogVisible = false;
			this.$emit("closed");
		},
	},
	mounted() {},
	created() {},
};
</script>

<style scoped></style>
