import { h, resolveComponent } from "vue";

//组件参数数据
export const componentsData = {};

// 搜索model
export const listQuery = {
	code: null,
	status: null,
	deviceId: null,
	abnormalId: null,
	name: null,
	buildId: null,
	content: null,
	repairBy: null,
	repairOrgId: null,
	common: null,
	alertAt: null,
	typeNotLike: 'close',
};

// 搜索表单配置
export const searchConfig = function () {
	return {
		list: [
			{
				type: "input",
				label: "报警内容",
				model: "common",
				options: {},
			},
			{
				type: "datetimerange",
				label: "报警时间",
				model: "createdAt",
				options: {},
			},
		],
		config: {
			size: "default",
			onSearch: () => {
				this.handleSubmit();
			},
			model: this.listQuery,
		},
	};
};

// 表格展示配置
export const tableColumns = function () {
	return [
		{
			type: "checkbox",
			fixed: "center",
		},
		{
			label: "报警设备",
			prop: "deviceName",
			showOverflowTooltip: true,
			align: "left",
		},
		{
			label: "报警内容",
			prop: "common",
			showOverflowTooltip: true,
			align: "left",
		},
		{
			label: "报警时间",
			prop: "createdAt",
			align: "left",
			showOverflowTooltip: true,
			/*          render: (row) => {
          	const alertAt = row.alertAt;
            if (alertAt) {
              return alertAt.substr(0, 10);
            }
          	return '';
          }*/
		},
		{
			label: "更新时间",
			prop: "updatedAt",
			align: "left",
			showOverflowTooltip: true,
			/*          render: (row) => {
          	const alertAt = row.alertAt;
            if (alertAt) {
              return alertAt.substr(0, 10);
            }
          	return '';
          }*/
		},
		{
			label: "状态",
			prop: "status",
			showOverflowTooltip: true,
			align: "left",
			width: 120,
		},
		{
			prop: "opt",
			fixed: "right",
			label: "操作",
			align: "center",
			width: 120,
			render: (row) => {
				const optArr = [];
				const detailBtn = h(resolveComponent("wp-tip-button"), {
					content: "查看详情",
					type: "text",
					icon: "wp-icon-detail",
					onClick: () => {
						this.detail(row);
					},
				});
				const planBtn = h(resolveComponent("wp-tip-button"), {
					content: "报修确认",
					type: "text",
					icon: "wp-icon-add-record",
					onClick: () => {
						this.handleOpt("plan", row);
					},
				});

				const resolveBtn = h(resolveComponent("wp-tip-button"), {
					content: "处理完毕",
					type: "text",
					icon: "wp-icon-enable",
					onClick: () => {
						// 编辑
						this.handleOpt("resolve", row);
					},
				});

				const closeBtn = h(resolveComponent("wp-tip-button"), {
					content: "关闭报警",
					type: "text",
					icon: "wp-icon-default",
					onClick: () => {
						// 编辑
						this.handleOpt("close", row);
					},
				});
				if (row.status === "HANDLING") {
					optArr.push(resolveBtn);
				}
				if (row.status === "HANDLE") {
					optArr.push(planBtn);
					optArr.push(closeBtn);
				}

				return h("div", null, optArr);
			},
		},
	];
};

// 表格展示配置
export const editConfig = function () {
	return {
		labelWidth: "130px",
		labelPosition: "top",
		size: "medium",
		formItems: [
			{
				label: "编号",
				name: "code",
				value: "",
				component: "input",
				span: 12,
				options: {},
				rules: [{ required: true, message: "必填项", trigger: "blur" }],
			},
			{
				label: "状态",
				name: "status",
				value: "",
				component: "input",
				span: 12,
				options: {},
			},
			{
				label: "运维设备主键",
				name: "deviceId",
				value: "",
				component: "input",
				span: 12,
				options: {},
			},
			{
				label: "巡检异常主键",
				name: "abnormalId",
				value: "",
				component: "input",
				span: 12,
				options: {},
			},
			{
				label: "运维设备名称",
				name: "name",
				value: "",
				component: "input",
				span: 12,
				options: {},
			},
			{
				label: "位置",
				name: "buildName",
				value: "",
				component: "input",
				span: 12,
				options: {},
				rules: [{ required: true, message: "必填项", trigger: "blur" }],
			},
			{
				label: "报警内容",
				name: "content",
				value: "",
				component: "input",
				span: 12,
				options: {},
				rules: [{ required: true, message: "必填项", trigger: "blur" }],
			},
			{
				label: "作业人ID",
				name: "repairBy",
				value: "",
				component: "input",
				span: 12,
				options: {},
				rules: [{ required: true, message: "必填项", trigger: "blur" }],
			},
			{
				label: "作业部门",
				name: "repairOrgId",
				value: "",
				component: "input",
				span: 12,
				options: {},
				rules: [{ required: true, message: "必填项", trigger: "blur" }],
			},
			{
				label: "作业备注内容",
				name: "common",
				value: "",
				component: "input",
				span: 12,
				options: {},
				rules: [{ required: true, message: "必填项", trigger: "blur" }],
			},
			{
				label: "报警时间",
				name: "alertAt",
				value: "",
				component: "date",
				span: 12,
				options: {
					type: "datetime",
					format: "YYYY-MM-DD HH:mm:ss",
				},
			},
		],
	};
};

export const formConfig = function () {
	return {
		labelWidth: "130px",
		labelPosition: "top",
		size: "medium",
		formItems: [
			{
				label: "作业部门",
				name: "repairOrgId",
				component: "inputTree",
				defaultText: "repairOrgName",
				disabled: "!!$.repairOrgId && $.status === 'HANDLING'",
				span: 12,
				options: {
					request: this.$API["uc/org"].getOrgTree.get,
					items: [],
				},
				rules: [{ required: true, message: "必填项", trigger: "blur" }],
			},
			{
				label: "作业人",
				name: "repairBy",
				value: "",
				component: "selectRemote",
				disabled: "!!$.repairBy && $.status === 'HANDLING' ",
				span: 12,
				options: {
					queryKey: "keyword",
					request: this.$API["uc/user"].pageSimple.get,
					params: { status: "ENABLED", pageSize: 1000 },
					placeholder: "请选择",
				},
				rules: [{ required: true, message: "必填项", trigger: "blur" }],
			},
			{
				label: "备注说明",
				name: "common",
				value: "",
				component: "textarea",
				disabled: "$.status === 'HANDLING'",
				span: 24,
				options: {},
				rules: [{ required: true, message: "必填项", trigger: "blur" }],
			},
			{
				label: "作业说明",
				name: "repairCommon",
				value: "",
				component: "textarea",
				span: 24,
				hideHandle: "$.status !== 'HANDLING'",
				options: {},
				rules: [{ required: true, message: "必填项", trigger: "blur" }],
			},
		],
	};
};
