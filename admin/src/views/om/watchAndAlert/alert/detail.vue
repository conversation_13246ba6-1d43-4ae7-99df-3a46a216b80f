<template>
	<div class="drawer detail">
		<el-drawer
			v-model="visible"
			:size="640"
			custom-class="detail-drawer"
			destroy-on-close
			@closed="$emit('closed')"
		>
			<template #header>
				<div class="title">
					<div class="line"></div>
					<span>{{ title }}</span>
				</div>
			</template>
			<template #default>
				<div class="content">
					<el-form
						ref="form"
						:model="form"
						class="form-item"
						label-position="left"
						label-width="80px"
					>
						<div>
							<div class="drawer-item">
								<div class="drawer-item-title">
									<img :src="drawerTitle" alt="基础信息" />
									<span>基础信息</span>
								</div>
							</div>
							<el-row :gutter="18" class="mt10">
								<el-col :span="12">
									<el-form-item label="报警设备">
										{{ form.deviceName }}
									</el-form-item>
								</el-col>
								<el-col :span="12">
									<el-form-item label="报警时间">
										{{ form.createdAt }}
									</el-form-item>
								</el-col>
								<el-col :span="24">
									<el-form-item label="报警内容">
										{{ form.common }}
									</el-form-item>
								</el-col>
							</el-row>
						</div>
						<div v-if="form.status === 'HANDLED'">
							<div class="drawer-item">
								<div class="drawer-item-title">
									<img :src="drawerTitle" alt="处理信息" />
									<span>处理信息</span>
								</div>
							</div>
							<el-row :gutter="18" class="mt10">
								<el-col :span="12">
									<el-form-item label="作业部门">
										{{ form.repairOrgName }}
									</el-form-item>
								</el-col>
								<el-col :span="12">
									<el-form-item label="作业人">
										{{ form.repairByName }}
									</el-form-item>
								</el-col>
								<!--								<el-col :span="12">-->
								<!--									<el-form-item label="处理方式">-->
								<!--										<span style="color:#009FA8;">-->
								<!--										{{searchKey(form.resultType)}}-->
								<!--										</span>-->
								<!--									</el-form-item>-->
								<!--								</el-col>-->
								<el-col :span="24">
									<el-form-item label="备注说明">
										{{ form.common }}
									</el-form-item>
								</el-col>
								<el-col :span="24">
									<el-form-item label="作业说明">
										{{ form.repairCommon }}
									</el-form-item>
								</el-col>
							</el-row>
						</div>
					</el-form>
				</div>
			</template>
		</el-drawer>
	</div>
</template>

<script>
import { CTX } from "@/api/config";
import tool from "@/utils/tool";
import commonEnum from "@/api/moudles/om/common";

const PREFIX = `${CTX.UPLOAD}`;
const token = tool.cookie.get("TOKEN");
export default {
	name: "alertDetail",
	components: {},
	data() {
		return {
			visible: false,
			title: null,
			form: {},
			drawerTitle: require("@/assets/images/drawer/drawer_icon_title.png"),
		};
	},
	methods: {
		open() {
			this.visible = true;
			return this;
		},
		getPage() {},
		setData(data) {
			Object.assign(this.form, data);
			this.title = "安全报警详情";
		},
		closed() {
			this.visible = false;
		},
		searchKey(resultType) {
			return commonEnum.searchKeys(
				commonEnum.alertResultType,
				resultType
			);
		},
	},
};
</script>

<style lang="scss" scoped>
.el-form-item__label {
	&::before {
		border-radius: 100%;
		width: 4px;
		height: 4px;
		content: "";
		background-color: #009fa8;
		display: inline-block;
		vertical-align: middle;
		position: inherit;
		left: -8px;
		top: 50%;
	}
}

.el-form-item {
	margin-bottom: 10px;
	font-size: 14px;
	line-height: 0;
}
</style>
