<template>
	<el-container>
		<!-- Left Aside for Tree -->
		<el-aside width="auto">
			<el-container>
				<el-header style="padding-top: 10px">
						<el-input
							v-model.trim="filterText"
							clearable
							placeholder="输入关键字过滤"
						></el-input>
				</el-header>
				<el-main class="nopadding">
					<el-tree
						ref="categoryTreeRef"
						:data="categoryTreeData"
						:props="defaultProps"
						:filter-node-method="filterNode"
						node-key="id"
						:expand-on-click-node="false"
						default-expand-all
						:highlight-current="true"
						class="menu"
						@node-click="handleNodeClick"
					>
						<template #default="{ node, data }">
							<span class="custom-tree-node">
								<span>{{ node.label }}</span>
							</span>
						</template>
					</el-tree>
				</el-main>
			</el-container>
		</el-aside>

		<!-- Right Side: Main Content -->
		<el-container>
			<el-header style="margin-top: 0">
				<div class="left-panel">
					<div class="left-panel-search">
						<wp-search-form :data="searchConfig"> </wp-search-form>
					</div>
				</div>
			</el-header>
			<!--      	<el-header style="padding-top: 13px">
							<el-button type="primary" @click="add">新增</el-button>
						</el-header>-->
			<el-main class="nopadding">
				<wpTable
					ref="table"
					:apiObj="apiObj"
					:column="columns"
					row-key="id"
					stripe
					@selection-change="handleSelectionChange"
				>
					<template #status="scope">
						<el-tag
							v-if="scope.row.status === 'HANDLE'"
							color="#FF636F"
							effect="dark"
							round
						>
							{{ searchStatus(scope.row.status) }}
						</el-tag>
						<el-tag
							v-if="scope.row.status === 'HANDLING'"
							color="#5CB8FF"
							effect="dark"
							round
						>
							{{ searchStatus(scope.row.status) }}
						</el-tag>
						<el-tag
							v-if="scope.row.status === 'HANDLED'"
							color="#48C7A2 "
							effect="dark"
							round
						>
							{{ searchStatus(scope.row.status) }}
						</el-tag>
						<el-tag
							v-if="scope.row.status === 'CLOSED'"
							color="#A0A0A0"
							effect="dark"
							round
						>
							{{ searchStatus(scope.row.status) }}
						</el-tag>
					</template>
				</wpTable>
			</el-main>
			</el-container>
		</el-container>

		<!-- Modals -->
		<dialog-modal
			v-if="visible"
			@closed="closed"
			@succes="getPage"
			ref="DialogModal"
		>
		</dialog-modal>
		<detail-modal
			v-if="showDetail"
			@closed="closed"
			ref="DetailModal"
		></detail-modal>
</template>

<script>
import { listQuery, searchConfig, tableColumns } from "./config";
import dialogModal from "./dialog";
import detailModal from "./detail";
import commonEnum from "@/api/moudles/om/common";

export default {
	name: "AlertTable",
	components: {
		dialogModal,
		detailModal,
	},
	data() {
		return {
			apiObj: this.$API["dt/abnormal"].pageAbnormal,
			columns: tableColumns.bind(this)(),
			searchConfig: null,
			listQuery: Object.assign({}, listQuery),
			selections: [],
			visible: false,
			showDetail: false,
			dialogVisible: false,
			activeName: "first",
			emptyText: "暂无数据",
			// 类别树相关数据
			categoryTreeData: [],
			filterText: "",
			defaultProps: {
				children: "children",
				label: "name",
			},
		};
	},
	computed: {},
	watch: {
		filterText(val) {
			this.$refs.categoryTreeRef.filter(val);
		},
	},
	created() {
		this.searchConfig = searchConfig.bind(this)();
	},
	mounted() {
		this.getCategoryTree();
	},
	methods: {
		// 获取类别树
		async getCategoryTree() {
			try {
				const res = await this.$API[
					"dt/category"
				].getTree.get({type:"DT_DEVICE"});
				if (res.success && res.data) {
					res.data.forEach(obj=>{obj.id=null})
					this.categoryTreeData = res.data;
				} else {
					this.$message.error(
						"获取报警设备类别失败: " + (res.msg || "未知错误")
					);
					this.categoryTreeData = [
						{ id: null, name: "全部类别", children: [] },
					];
				}
			} catch (error) {
				console.error("Error fetching category tree:", error);
				this.$message.error("获取报警设备类别时出错");
				this.categoryTreeData = [
					{ id: null, name: "全部类别", children: [] },
				];
			}
		},
		filterNode(value, data) {
			if (!value) return true;
			return data.name && data.name.includes(value);
		},
		handleNodeClick(data) {
			this.listQuery.deviceId = data.id;
			this.handleSubmit();
		},
		clearCategoryFilter() {
			this.listQuery.deviceId = null;
			if (this.$refs.categoryTreeRef) {
				this.$refs.categoryTreeRef.setCurrentKey(null);
			}
			this.handleSubmit();
		},
		getPage() {
			this.$refs.table.upData(this.listQuery);
		},
		handleSubmit() {
			this.$refs.table.upData(this.listQuery);
		},
		openDetail(row) {
			this.handleOpt("detail", row);
		},
		handleSelectionChange(val) {
			this.selections = val;
		},
		async handleOpt(type, row) {
			switch (type) {
				case "plan":
					row.type = "plan";
					row.title = "报警确认";
					row.show = true;
					break;
				case "resolve":
					row.type = "resolve";
					row.title = "处理完毕";
					row.show = true;
					break;
				case "close":
					row.type = "close";
					row.title = "关闭报警";
					row.show = false;
					break;
				default:
			}
			this.visible = true;
			this.$nextTick(() => {
				this.$refs.DialogModal.open(row);
			});
		},
		closed() {
			this.visible = !this.visible;
			this.getPage();
		},
		searchStatus(status) {
			return commonEnum.searchKeys(commonEnum.alertStatus, status);
		},

		async detail(row) {
			this.showDetail = true;
			let result = await this.$API["dt/abnormal"].getWithDetail.get(row.id);
			this.$nextTick(() => {
				this.$refs.DetailModal.open().setData(result.data);
			});
		},
	},
};
</script>
<style lang="scss" scoped>
@import "~@/style/tabs_fix.scss";

.el-aside {
	border-right: 1px solid #eee;
	display: flex;
	flex-direction: column;
}

.el-aside .el-header {
	line-height: normal;
	border-bottom: 1px solid #eee;
	height: auto;
	padding-bottom: 10px;
}

.menu {
	height: 100%;
	overflow-y: auto;
}

.custom-tree-node {
	flex: 1;
	display: flex;
	align-items: center;
	justify-content: space-between;
	font-size: 14px;
	padding-right: 8px;
}

.tree-header {
	width: 100%;
}

.tree-title {
	margin: 0 0 10px 0;
	font-size: 16px;
	font-weight: 600;
	color: #303133;
	text-align: center;
	padding-bottom: 8px;
	border-bottom: 1px solid #ebeef5;
}

.el-container > .el-container {
	flex-direction: column;
}
</style>
