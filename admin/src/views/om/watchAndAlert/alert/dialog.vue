<template>
	<el-dialog
		v-model="dialogVisible"
		:title="title"
		:visible="dialogVisible"
		@close="handleClose"
		width="50%"
	>
		<wp-form
			ref="form"
			v-model="form"
			:config="config"
			:loading="loading"
			v-if="show"
		>
		</wp-form>
		<div v-else style="display: flex; justify-content: center">
			关闭报警后，该报警信息将处于“已关闭”状态，是否确认操作？
		</div>
		<template #footer>
			<div class="dialog-footer">
				<el-button @click="reset" v-if="show">重置</el-button>
				<el-button @click="showPlan" v-else>报警确认</el-button>
				<el-button type="primary" @click="handleOpt">
					{{ title }}
				</el-button>
			</div>
		</template>
	</el-dialog>
</template>
<script>
import { editConfig } from "../../repairAndExamine/failureRepair/config";

export default {
	name: "htDialog",
	data() {
		return {
			dialogVisible: false,
			title: "报修登记",
			form: {
				id: null,
				status: "",
				repairOrgId: null,
				repairBy: null,
				common: "",
				repairCommon: "",
				repairOrgName: "",
				title: "",
				type: null,
			},
			show: false,
			config: null,
			loading: false,
		};
	},

	methods: {
		handleClose() {
			this.dialogVisible = false;
			this.$emit("closed");
		},
		open(row) {
			Object.assign(this.form, row);
			this.dialogVisible = true;
			if (row.title) {
				this.title = row.title;
			}
			this.show = row.show;
			this.config = editConfig.bind(this)();
			return this;
		},

		async handleOpt() {
			let result = {};
			let param = {
				id: this.form.id,
				status: null,
				repairOrgId: null,
				repairBy: null,
				common: null,
				repairCommon: null,
				repairOrgName: null,
				type: "",
			};
			param = this.$TOOL.ObjFilter(param, this.form);
			switch (param.type) {
				case "plan":
					param.resultType = "APCL";
					param.status = "HANDLING";
					break;
				case "resolve":
					param.resultType = "CLWB";
					param.status = "HANDLED";
					break;
				case "close":
					break;
				default:
			}
			if (this.show) {
				this.$refs.form.validate(async (valid) => {
					if (!valid) {
						return;
					}
					result = await this.$API["om/alert"].edit.post(param);
					if (result.success) {
						this.$message.success("操作成功" || result.msg);
						this.handleClose();
						this.$emit("success");
					}
				});
			} else {
				result = await this.$API["dt/abnormal"].close.get(param.id);
				if (result.success) {
					this.$message.success("操作成功" || result.msg);
					this.handleClose();
					this.$emit("success");
				}
			}
		},

		reset() {
			this.$refs.form.resetFields();
		},

		showPlan() {
			this.title = "报警确认";
			this.show = true;
		},
	},
};
</script>
