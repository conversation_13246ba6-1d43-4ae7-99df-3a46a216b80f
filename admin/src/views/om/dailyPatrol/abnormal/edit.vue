<template>
	<div class="drawer detail">
		<el-drawer
			v-model="visible"
			:size="640"
			custom-class="detail-drawer"
			destroy-on-close
			@closed="$emit('closed')"
		>
			<template #header>
				<div class="title">
					<div class="line"></div>
					<span v-if="!this.form.id">添加巡检异常</span>
					<span v-else>编辑巡检异常</span>
				</div>
			</template>
			<template #default>
				<div class="drawer-content">
					<el-form
						:model="form"
						ref="form"
						class="form-item"
						label-width="90px"
						label-position="top"
						:rules="rules"
					>
						<el-form-item
							label="设备名称 "
							prop="deviceId"
							style="margin-bottom: 15px; width: 37% !important"
						>
							<wp-select-remote
								v-model="form.deviceId"
								:multiple="false"
								:defaultText="222"
								:params="{
									pageSize: 1000,
									status: 'ENABLED',
								}"
								:placeholder="请输入"
								:request="this.$API['dt/device-info'].deviceInfoPage.get"
								style="width: 100%"
							>
							</wp-select-remote>
						</el-form-item>
						<el-form-item
							label="巡检时间"
							prop="patrolAt"
							style="margin-bottom: 15px; width: 240px"
						>
							<el-date-picker
								v-model="form.patrolAt"
								type="datetime"
								:editable="false"
								:disabled-date="validateDate"
								value-format="YYYY-MM-DD HH:mm:ss"
							/>
						</el-form-item>
						<el-form-item
							label="登记人"
							prop="checkBy"
							style="margin-bottom: 15px; width: 240px"
						>
					<wp-select-remote
						v-model="form.checkBy"
											  :multiple="false"
											  :params="{status: 'ENABLED', pageSize: 1000}"
											  :placeholder="请输入"
											  :queryKey="keyword"
											  :request="this.$API['uc/user'].pageSimple.get"
											  style="width: 100%;">
							</wp-select-remote>
						</el-form-item>
						<el-form-item
							class="fullWidth"
							label="异常概述"
							prop="result"
							style="margin-bottom: 15px"
						>
							<el-input
								v-model.trim="form.result"
								maxlength="50"
								placeholder="请概述异常概述"
								show-word-limit
								type="textarea"
							></el-input>
						</el-form-item>
						<el-form-item
							class="fullWidth"
							label="异常详情"
							style="margin-bottom: 15px"
						>
							<el-input
								v-model.trim="form.resultDetail"
								maxlength="500"
								placeholder="请输入异常详情"
								show-word-limit
								type="textarea"
							></el-input>
						</el-form-item>
						<el-form-item
							class="fullWidth"
							label="相关图片"
							prop="imageFileList"
							style="margin-bottom: 15px"
						>
							<wp-upload-multiple
								v-model="form.imageFileList"
								:limit="3"
								draggable
								tip="最多上传3个文件,单个文件不要超过10M,请上传图像格式文件"
							></wp-upload-multiple>
						</el-form-item>
					</el-form>
				</div>
			</template>
			<template #footer>
				<div class="drawer-footer">
					<div class="drawer-button">
						<el-button @click="closed">取消</el-button>
						<el-button type="primary" @click="handleSubmit"
							>提交</el-button
						>
					</div>
				</div>
			</template>
		</el-drawer>
	</div>
</template>

<script>
import commonEum from "@/api/moudles/om/common";

export default {
	name: "edit",
	data() {
		return {
			visible: false,
			zoneIdRequest: null,
			zones: [],
			checkBys: [],
			form: {
				id: "",
				imageFileList: [],
				patrolAt: null,
				checkBy: "",
				result: "",
				resultDetail: "",
				updatedAt: null,
				updatedBy: null,
				zoneId: "",
			},
			loading: false,
			rules: {
				deviceId: [
					{
						required: true,
						message: "请选择设备名称",
						trigger: "change",
					},
				],
				patrolAt: [
					{
						type: "date",
						required: true,
						message: "请选择巡检时间",
						trigger: "blur",
					},
				],
				checkBy: [
					{
						required: true,
						message: "请选择登记人",
						trigger: "change",
					},
				],
				result: [
					{
						required: true,
						message: "请输入异常概述",
						trigger: "blur",
					},
				],
				imageFileList: [
					{
						required: true,
						message: "请上传相关图片",
						trigger: "change",
					},
				],
			},
		};
	},
	created() {},
	methods: {
		open() {
			this.visible = true;
			this.form.deviceId = null;
			this.form.zoneId = null;
			this.form.patrolAt = null;
			this.form.checkBy = null;
			this.form.result = null;
			this.form.resultDetail = null;
			this.form.imageFileList = null;
			this.zones = [];
			this.checkBys = [];
			return this;
		},

		setData(data) {
			// this.form = this.$TOOL.ObjFilter(this.form, data);
			console.log("edit.data", data);
			Object.assign(this.form, data);
			console.log("edit.form", this.form);
			// this.form.deviceId = data.id;
			this.$API["om/planZone"].page
				.get({ deviceId: this.form.deviceId })
				.then((res) => {
					console.log(" res.data", res.data);
					this.zones = res.data.list;
					console.log("zones", this.zones);
				});

			this.$API["om/planUser"].page
				.get({ deviceId: this.form.deviceId })
				.then((res) => {
					console.log(" res.data", res.data);
					this.checkBys = res.data.list;
					console.log("checkBys", this.checkBys);
				});
		},
		handleClear() {
			console.log("form.deviceId", this.form.deviceId);
			if (!this.form.deviceId || this.form.deviceId === "") {
				this.zones = [];
				this.form.zoneId = null;
				this.checkBys = [];
				this.form.checkBy = null;
			}
		},

		async deviceIdChange(name, event) {

		},

		async getData(id) {
			const res = await this.$API["om/abnormal"].getWithDetail.get(id);

			if (res.success) {
				this.deviceInfo = res.data;
				this.deviceInfo.countriesArea = commonEum.searchKeys(
					commonEum.countriesArea,
					this.deviceInfo.countriesArea
				);
				if (this.deviceInfo.imageFileRelList.length > 0) {
					this.deviceInfo.fileId =
						this.deviceInfo.imageFileRelList[0].fileId;
				}
				this.form.deviceId = this.deviceInfo.id;
			} else {
				this.$message.error(res.msg || "操作失败");
			}
		},

		async handleSubmit() {
			this.$refs.form.validate(async (valid) => {
				if (!valid) {
					return;
				}
				let res;
				this.form.updatedBy = null;
				this.form.updatedAt = null;
				if (this.form.id) {
					res = await this.$API["om/abnormal"].edit.post(this.form);
				} else {
					res = await this.$API["om/abnormal"].add.post(this.form);
				}
				if (res.success) {
					this.$message.success(res.msg || "操作成功");
					this.visible = false;
					this.$emit("success");
					this.$emit("closed");
				} else {
					this.$message.error(res.msg || "操作失败");
				}

				return res;
			});
		},

		closed() {
			this.visible = this.againFlag;
			this.$emit("closed");
		},
		validateDate(time) {
			return time.getTime() > Date.now();
		},
	},
};
</script>

<style lang="scss" scoped>
.content {
	padding: 20px;
}

.out-flex-container {
	margin-bottom: 10px;
}

.inner-flex-container {
	justify-content: space-around;
}
</style>
