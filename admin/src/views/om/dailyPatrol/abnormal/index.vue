<template>
	<el-container>
		<el-header>
			<div class="left-panel">
				<div class="left-panel-search">
					<wp-search-form :data="searchConfig"></wp-search-form>
				</div>
			</div>
		</el-header>
		<el-header style="padding-top: 13px">
			<el-button type="primary" @click="add">登记异常</el-button>
		</el-header>
		<el-main class="nopadding">
			<wpTable
				ref="table"
				:apiObj="apiObj"
				:column="columns"
				row-key="id"
				stripe
				@selection-change="handleSelectionChange"
			>
				<template #status="scope">
					<el-tag
						v-if="scope.row.status === 'ENABLED'"
						color="#F0AD57"
						effect="dark"
						round
					>
						{{ searchStatus(scope.row.status) }}
					</el-tag>
					<el-tag
						v-if="scope.row.status === 'DISABLED'"
						color="#48C7A2"
						effect="dark"
						round
					>
						{{ searchStatus(scope.row.status) }}
					</el-tag>
				</template>
			</wpTable>
		</el-main>
		<edit-modal
			v-if="visible"
			ref="editModal"
			@closed="visible = !visible"
			@success="getPage"
		></edit-modal>
		<detail-modal
			v-if="showDetail"
			ref="detailModal"
			@closed="showDetail = !showDetail"
		></detail-modal>
		<deal-modal
			:key="timer"
			v-if="dealShow"
			ref="dealModal"
			@closed="dealShow = !dealShow"
			@success="getPage"
		></deal-modal>
	</el-container>
</template>

<script>
import { tableColumns, listQuery, searchConfig } from "./config";
import editModal from "./edit";
import detailModal from "./detail";
import dealModal from "./deal";
import commonEnum from "@/api/moudles/om/common";
export default {
	name: "AbnormalTable",
	components: {
		editModal,
		detailModal,
		dealModal,
	},
	data() {
		return {
			timer: new Date().getTime(),
			apiObj: this.$API["om/abnormal"].page,
			columns: tableColumns.bind(this)(),
			searchConfig: null,
			listQuery: Object.assign({}, listQuery),
			selections: [],
			visible: false,
			showDetail: false,
			dealShow: false,
			dialogVisible: false,
		};
	},
	computed: {},
	watch: {},
	created() {
		this.searchConfig = searchConfig.bind(this)();
	},
	mounted() {},
	methods: {
		getPage() {
			this.$refs.table.upData(this.listQuery);
		},
		handleSubmit() {
			this.$refs.table.upData(this.listQuery);
		},
		openDetail(row) {
			this.handleOpt("detail", row);
		},
		handleSelectionChange(val) {
			this.selections = val;
		},
		async handleOpt(type, row) {
			let result = null;
			switch (type) {
				case "detail":
					this.showDetail = true;
					result = await this.$API["om/abnormal"].getWithDetail.get(
						row.id
					);
					this.$nextTick(() => {
						this.$refs.detailModal.open().setData(result.data);
					});
					break;
				case "edit":
					this.visible = true;
					result = await this.$API["om/abnormal"].getWithDetail.get(
						row.id
					);
					this.$nextTick(() => {
						this.$refs.editModal.open().setData(result.data);
					});
					break;
				case "deal":
					this.dealShow = true;
					this.timer = new Date().getTime();
					this.$nextTick(() => {
						this.$refs.dealModal.open(row.id);
					});
					break;
				case "delete":
					this.$confirm("确认删除该条记录吗?", "提示", {
						confirmButtonText: "确定",
						cancelButtonText: "取消",
						type: "warning",
					})
						.then(async () => {
							result = await this.$API[
								"dm/abnormal"
							].delete.delete([row.id]);
							if (result.success) {
								this.$message.success("删除成功");
								this.getPage();
							} else {
								this.$message.error(result.msg || "删除失败");
							}
						})
						.catch(() => {
							this.$message.info("已取消删除");
						});
					break;
				default:
			}
		},
		add() {
			this.visible = true;
			this.$nextTick(() => {
				this.$refs.editModal.open();
			});
		},
		searchStatus(status) {
			return commonEnum.searchKeys(
				commonEnum.patrolAbnormalStatus,
				status
			);
		},
	},
};
</script>
<style scoped></style>
