<template>
	<div class="drawer detail">
		<el-drawer
			v-model="visible"
			:size="widthDrawer"
			custom-class="detail-drawer"
			destroy-on-close
			@closed="closed"
		>
			<template #header>
				<div class="title">
					<div class="line"></div>
					<span>{{ titleDrawer }}</span>
				</div>
			</template>
			<template #default>
				<div class="drawer-content detail">
					<el-form ref="from" class="form-item">
						<div
							class="drawer-item"
							v-if="abnormal.status === 'DISABLED'"
						>
							<div class="drawer-item-title">
								<img :src="drawerTitle" />
								<span>基本信息</span>
							</div>
						</div>
						<el-form-item label="设备名称">
							{{ abnormal.deviceName }}
						</el-form-item>
						<el-form-item label="状态">
							<el-tag
								v-if="abnormal.status === 'ENABLED'"
								color="#F0AD57"
								effect="dark"
								round
							>
								{{
									searchStatus(abnormal.status, "status")
								}}
							</el-tag>
						</el-form-item>
						<el-form-item label="巡检时间">
							{{ abnormal.patrolAt }}
						</el-form-item>
						<el-form-item label="登记人员">
							{{ abnormal.checkByName }}
						</el-form-item>
						<el-form-item label="登记时间">
							{{ abnormal.checkAt }}
						</el-form-item>
						<el-col>
							<el-form-item label="异常概述">
								{{ abnormal.result }}
							</el-form-item>
						</el-col>
						<el-col>
							<el-form-item label="异常详情">
								{{ abnormal.resultDetail }}
							</el-form-item>
						</el-col>
						<el-col>
							<el-form-item label="异常图片" style="height: auto">
								<ul class="el-upload-list image">
									<li
										v-for="item in abnormal.imageFileList"
										:key="item.fileId"
										class="el-upload-list__item"
									>
										<el-image
											:preview-src-list="[
												solutionSrc(item.fileId),
											]"
											:src="solutionSrc(item.fileId)"
											alt="点击查看大图"
											preview-teleported
											style="width: 120px; height: 120px"
											title="点击查看大图"
										>
											<template #error>
												<el-image
													:src="$imageDefault"
													style="
														width: 120px;
														height: 120px;
													"
												></el-image>
											</template>
										</el-image>
									</li>
								</ul>
							</el-form-item>
						</el-col>

						<div
							class="drawer-item"
							v-if="abnormal.status === 'DISABLED'"
						>
							<div class="drawer-item-title">
								<img :src="drawerTitle" />
								<span>处理信息</span>
							</div>
						</div>
						<el-col>
							<el-form-item
								label="处理方式"
								v-if="abnormal.status === 'DISABLED'"
							>
								<span>{{
									searchStatus(abnormal.dealType, "type")
								}}</span>
							</el-form-item>
						</el-col>
						<el-form-item
							label="处理人员"
							v-if="abnormal.status === 'DISABLED'"
						>
							{{ abnormal.dealByName }}
						</el-form-item>
						<el-form-item
							label="处理时间"
							v-if="abnormal.status === 'DISABLED'"
						>
							{{ abnormal.dealAt }}
						</el-form-item>
						<el-col>
							<el-form-item
								label="处理说明"
								v-if="abnormal.status === 'DISABLED'"
							>
								{{ abnormal.dealCommon }}
							</el-form-item>
						</el-col>
					</el-form>
				</div>
			</template>
			<template #footer>
				<div class="drawer-footer">
					<div class="drawer-button">
						<el-button
							v-if="this.abnormal.status === 'ENABLED'"
							@click="handleSubmit('delete')"
							type="danger"
							>删除异常
						</el-button>
						<el-button
							v-if="this.abnormal.status === 'ENABLED'"
							@click="handleSubmit('edit')"
							>编辑异常
						</el-button>
						<el-button
							v-if="this.abnormal.status === 'ENABLED'"
							@click="handleSubmit('deal')"
							type="primary"
							>处理异常
						</el-button>
						<el-button
							v-if="this.abnormal.status === 'DISABLED'"
							@click="handleSubmit('delete')"
							plain
							type="danger"
							>删除记录
						</el-button>
					</div>
				</div>
			</template>
		</el-drawer>
	</div>
	<deal-modal
		v-if="dealShow"
		ref="dealModal"
		@closed="dealShow = !dealShow"
		@success="getPage"
	></deal-modal>
	<edit-modal
		v-if="editVisible"
		ref="editModal"
		@closed="editVisible = !editVisible"
		@success="getPage"
	></edit-modal>
</template>

<script>
import dealModal from "./deal";
import editModal from "./edit";
import commonEnum from "@/api/moudles/om/common";
import uploadConfig from "@/config/upload";

export default {
	name: "abnormalDetail",
	components: {
		dealModal,
		editModal,
	},
	props: {
		widthDrawer: { type: Number, default: 560 }, // 弹出框是否展示
		titleDrawer: { type: String, default: "巡检异常详情" }, // 弹出框标题
	},
	data() {
		return {
			editVisible: false,
			dealShow: false,
			visible: false,
			title: "巡检异常详情",
			type: "",
			form: {},
			drawerTitle: require("@/assets/images/drawer/drawer_icon_title.png"),
			abnormal: {},
		};
	},
	methods: {
		open() {
			this.visible = true;
			return this;
		},

		setData(data, type) {
			console.log("detail.data", data);
			this.type = type;
			this.abnormal = data;
			if (this.abnormal.imageFileList.length > 0) {
				this.abnormal.fileId = this.abnormal.imageFileList[0].fileId;
			}
			// this.form.abnormal.countriesArea = commonEnum.searchKeys(commonEnum.countriesArea, this.form.abnormal.countriesArea);
		},

		async handleSubmit(type) {
			let result = null;
			switch (type) {
				case "edit":
					this.editVisible = true;
					result = await this.$API["om/abnormal"].getWithDetail.get(
						this.abnormal.id
					);
					this.$nextTick(() => {
						this.$refs.editModal.open().setData(result.data);
					});
					break;
				case "deal":
					this.dealShow = true;
					this.$nextTick(() => {
						this.$refs.dealModal.open(this.abnormal.id);
					});
					break;
				case "delete":
					this.$confirm("确认删除该条记录吗?", "提示", {
						confirmButtonText: "确定",
						cancelButtonText: "取消",
						type: "warning",
					})
						.then(async () => {
							result = await this.$API[
								"dm/abnormal"
							].delete.delete([this.abnormal.id]);
							if (result.success) {
								this.$message.success("删除成功");
								this.$emit("success");
								this.$emit("closed");
							} else {
								this.$message.error(result.msg || "删除失败");
							}
						})
						.catch(() => {
							this.$message.info("已取消删除");
						});
					break;
				default:
			}
		},

		cancel() {},

		solutionSrc(fileId) {
			return uploadConfig.solutionSrc(fileId);
		},

		closed() {
			this.visible = false;
			this.form = {};
			this.$emit("closed");
		},

		goDelete() {
			let result = {};
			this.$confirm("确认删除该条记录吗?", "提示", {
				confirmButtonText: "确定",
				cancelButtonText: "取消",
				type: "warning",
			}).then(async () => {
				result = await this.$API["dm/deviceRepair"].delete.delete([
					this.form.id,
				]);
				if (result.success) {
					this.$message.success(result.msg || "操作成功");
					this.closed();
				} else {
					this.$message.error(result.msg || "操作失败");
				}
			});
		},

		searchStatus(status, type) {
			if (type === "status") {
				return commonEnum.searchKeys(
					commonEnum.patrolAbnormalStatus,
					status
				);
			}

			return commonEnum.searchKeys(
				commonEnum.patrolAbnormalDealType,
				status
			);
		},
	},
};
</script>

<style lang="scss" scoped>
:deep(.el-tabs__content) {
	overflow: visible;
}
</style>
