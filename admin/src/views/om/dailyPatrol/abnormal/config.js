import { defineAsyncComponent, h, resolveComponent } from "vue";
import commonEnum from "@/config/common";
const ActionMenu = defineAsyncComponent(() => import("./actionMenu"));

//组件参数数据
export const componentsData = {};
// 搜索model
export const listQuery = {
	code: null,
	status: null,
	result: null,
	resultDetail: null,
	zoneId: null,
	zoneName: null,
	planId: null,
	planName: null,
	patrolAt: null,
	checkBy: null,
	checkAt: null,
};

// 搜索表单配置
export const searchConfig = function () {
	return {
		list: [
			{
				type: "selectRemote",
				label: "设备名称",
				model: "deviceId",
				options: {
					request: this.$API['dt/device-info'].deviceInfoPage.get,
					items: [],
					props: {
						children: "children",
						label: "name",
					},
				},
			},

			{
				type: "datetimerange",
				label: "时间范围",
				model: "patrolAt",
				options: {
					placeholder: "选择时间范围",
				},
			},
		],
		config: {
			size: "default",
			onSearch: () => {
				this.handleSubmit();
			},
			model: this.listQuery,
		},
	};
};

// 表格展示配置
export const tableColumns = function () {
	return [
		{
			type: "checkbox",
			fixed: "center",
		},
		{
			label: "设备名称",
			prop: "deviceName",
			showOverflowTooltip: true,
			align: "left",
		},

			{
			label: "巡检时间",
			prop: "patrolAt",
			align: "left",
			showOverflowTooltip: true,
			/*          render: (row) => {
          	const patrolAt = row.patrolAt;
            if (patrolAt) {
              return patrolAt.substr(0, 10);
            }
          	return '';
          }*/
		},
		{
			label: "登记时间",
			prop: "checkAt",
			showOverflowTooltip: true,
			align: "left",
		},
		{
			label: "登记人",
			prop: "checkByName",
			showOverflowTooltip: true,
			align: "left",
		},
		{
			label: "异常概述",
			prop: "result",
			showOverflowTooltip: true,
			align: "left",
		},
		{
			label: "异常详情",
			prop: "resultDetail",
			showOverflowTooltip: true,
			align: "left",
		},
		{
			label: "任务状态",
			prop: "status",
			showOverflowTooltip: true,
			align: "left",
		},
		{
			prop: "opt",
			fixed: "right",
			label: "操作",
			align: "center",
			render: (row) => {
				const optArr = [];

				const menuBtn = h(ActionMenu, {
					item: row,
				});

				const detailBtn = h(resolveComponent("wp-tip-button"), {
					content: "详情",
					type: "text",
					icon: "wp-icon-detail",
					onClick: () => {
						this.handleOpt("detail", row);
					},
				});

				const dealBtn = h(resolveComponent("wp-tip-button"), {
					content: "处理",
					type: "text",
					icon: "wp-icon-add-record",
					onClick: () => {
						// 编辑
						this.handleOpt("deal", row);
					},
				});

				const editBtn = h(resolveComponent("wp-tip-button"), {
					content: "编辑",
					type: "text",
					icon: "wp-icon-edit",
					onClick: () => {
						// 编辑
						this.handleOpt("edit", row);
					},
				});

				const delBtn = h(resolveComponent("wp-tip-button"), {
					content: "删除",
					type: "text",
					icon: "wp-icon-delete",
					onClick: () => {
						// 编辑
						this.handleOpt("delete", row);
					},
				});

				optArr.push(detailBtn);
				if (row.status === "ENABLED") {
					optArr.push(editBtn);
					optArr.push(dealBtn);
				} else {
					optArr.push(delBtn);
				}
				return h(
					"div",
					{
						style: {
							display: "flex",
							"flex-direction": "row",
							"align-content": "center",
							"vertical-align": "middle",
							"align-items": "center",
						},
					},
					optArr
				);
			},
		},
	];
};

// 表格展示配置
export const editConfig = function () {
	return {
		labelWidth: "130px",
		labelPosition: "top",
		size: "medium",
		formItems: [
			{
				label: "编号",
				name: "code",
				value: "",
				component: "input",
				span: 12,
				options: {},
				rules: [{ required: true, message: "必填项", trigger: "blur" }],
			},
			{
				label: "状态",
				name: "status",
				value: "",
				component: "input",
				span: 12,
				options: {},
			},
			{
				label: "巡检异常概述",
				name: "result",
				value: "",
				component: "input",
				span: 12,
				options: {},
				rules: [{ required: true, message: "必填项", trigger: "blur" }],
			},
			{
				label: "巡检异常说明",
				name: "resultDetail",
				value: "",
				component: "input",
				span: 12,
				options: {},
				rules: [{ required: true, message: "必填项", trigger: "blur" }],
			},
			{
				label: "巡检区域主键",
				name: "zoneId",
				value: "",
				component: "input",
				span: 12,
				options: {},
			},
			{
				label: "巡检区域名称",
				name: "zoneName",
				value: "",
				component: "input",
				span: 12,
				options: {},
			},
			{
				label: "巡检计划主键",
				name: "planId",
				value: "",
				component: "input",
				span: 12,
				options: {},
			},
			{
				label: "巡检计划名",
				name: "planName",
				value: "",
				component: "input",
				span: 12,
				options: {},
			},
			{
				label: "巡检时间",
				name: "patrolAt",
				value: "",
				component: "date",
				span: 12,
				options: {
					type: "datetime",
					format: "YYYY-MM-DD HH:mm:ss",
				},
			},
			{
				label: "登记人",
				name: "checkBy",
				value: "",
				component: "input",
				span: 12,
				options: {},
			},
			{
				label: "登记时间",
				name: "checkAt",
				value: "",
				component: "input",
				span: 12,
				options: {},
			},
		],
	};
};
