<template>
	<div class="drawer detail">
		<el-drawer
			v-model="visible"
			:size="640"
			custom-class="detail-drawer"
			destroy-on-close
			@closed="$emit('closed')"
		>
			<template #header>
				<div class="title">
					<div class="line"></div>
					<span>{{ title }}</span>
				</div>
			</template>
			<template #default>
				<div class="content">
					<el-form
						ref="form"
						:model="form"
						class="form-item"
						label-position="left"
						label-width="80px"
					>
						<div>
							<div class="drawer-item">
								<div class="drawer-item-title">
									<img :src="drawerTitle" alt="基础信息" />
									<span>基础信息</span>
								</div>
							</div>
							<el-row :gutter="18" class="mt10">
								<el-col :span="12">
									<el-form-item label="设备名称">
										{{ form.deviceName }}
									</el-form-item>
								</el-col>
								<el-col :span="12">
									<el-form-item label="作业部门">
										{{ form.repairOrgName }}
									</el-form-item>
								</el-col>
								<el-col :span="12">
									<el-form-item label="登记人员">
										{{ form.checkByName }}
									</el-form-item>
								</el-col>
								<el-col :span="12">
									<el-form-item label="维修人员">
										{{ form.repairByName }}
									</el-form-item>
								</el-col>
								<el-col :span="12">
									<el-form-item label="登记时间">
										{{ form.checkAt }}
									</el-form-item>
								</el-col>
								<el-col :span="12">
									<el-form-item label="维修时间">
										{{ form.repairAt }}
									</el-form-item>
								</el-col>
								<el-col :span="12">
									<el-form-item label="故障概述">
										{{ form.result }}
									</el-form-item>
								</el-col>
								<el-col :span="24">
									<el-form-item label="故障说明">
										{{ form.resultDetail }}
									</el-form-item>
								</el-col>
								<el-col :span="24">
									<el-form-item
										label="相关图片"
										style="height: auto"
									>
										<ul
											class="el-upload-list image"
											style="margin-top: 10px"
										>
											<li
												v-for="item in form.fileRelList"
												:key="item.fileId"
												class="el-upload-list__item"
											>
												<el-image
													:preview-src-list="[
														solutionSrc(
															item.fileId
														),
													]"
													:src="
														solutionSrc(item.fileId)
													"
													alt="点击查看大图"
													preview-teleported
													style="
														width: 120px;
														height: 120px;
													"
													title="点击查看大图"
												>
													<template #error>
														<el-image
															:src="$imageDefault"
															style="
																width: 120px;
																height: 120px;
															"
														></el-image>
													</template>
												</el-image>
											</li>
										</ul>
									</el-form-item>
								</el-col>
							</el-row>
						</div>
					</el-form>
				</div>
			</template>
			<template #footer>
				<div class="drawer-button">
					<el-button type="danger" plain @click="deleteData"
						>删除记录</el-button
					>
				</div>
			</template>
		</el-drawer>
	</div>
</template>

<script>
import uploadConfig from "@/config/upload";

export default {
	name: "deviceInfoDetail",
	components: {},
	data() {
		return {
			visible: false,
			dealShow: false,
			title: "维修详情",
			form: {
				id: null,
			},
			drawerTitle: require("@/assets/images/drawer/drawer_icon_title.png"),
		};
	},
	methods: {
		open() {
			this.visible = true;
			return this;
		},
		getPage() {
			this.$emit("closed");
			this.$emit("success");
		},
		setData(data) {
			Object.assign(this.form, data);
		},
		cancel() {},
		solutionSrc(fileId) {
			return uploadConfig.solutionSrc(fileId);
		},
		closed() {
			this.visible = false;
			this.$emit("closed");
		},
		addRepair() {},
		deleteData() {
			let result = {};
			this.$confirm("确认删除吗？", "提示", {
				confirmButtonText: "确定",
				cancelButtonText: "取消",
				type: "warning",
			})
				.then(async () => {
					result = await this.$API["om/deviceRepair"].delete.delete([
						this.form.id,
					]);
					this.$nextTick(() => {
						if (result.success) {
							this.$message.success(result.msg || "删除");
						}
					});
					this.closed();
				})
				.catch(() => {
					this.$message.info("已取消删除");
				});
		},
	},
};
</script>

<style lang="scss" scoped>
.el-form-item__label {
	&::before {
		border-radius: 100%;
		width: 4px;
		height: 4px;
		content: "";
		background-color: #009fa8;
		display: inline-block;
		vertical-align: middle;
		position: inherit;
		left: -8px;
		top: 50%;
	}
}

.el-form-item {
	margin-bottom: 10px;
	font-size: 14px;
	line-height: 0;
}
</style>
