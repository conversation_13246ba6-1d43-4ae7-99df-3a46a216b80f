<template>
	<el-container>
		<!-- Left Aside for Tree -->
		<el-aside width="auto">
			<el-container>
				<el-header style="padding-top: 10px">
						<el-input
							v-model.trim="filterText"
							clearable
							placeholder="输入关键字过滤"
						></el-input>
				</el-header>
				<el-main class="nopadding">
					<el-tree
						ref="categoryTreeRef"
						:data="categoryTreeData"
						:props="defaultProps"
						:filter-node-method="filterNode"
						node-key="id"
						:expand-on-click-node="false"
						default-expand-all
						:highlight-current="true"
						class="menu"
						@node-click="handleNodeClick"
					>
						<template #default="{ node, data }">
							<span class="custom-tree-node">
								<span>{{ node.label }}</span>
							</span>
						</template>
					</el-tree>
				</el-main>
			</el-container>
		</el-aside>

		<!-- Right Side: Main Content -->
		<el-container>
			<el-header style="margin-top: 0">
				<div class="left-panel">
					<div class="left-panel-search">
						<wp-search-form :data="searchConfig"></wp-search-form>
					</div>
				</div>
			</el-header>
			<el-header style="padding-top: 13px">
				<el-button type="primary" @click="add">添加检测</el-button>
			</el-header>
			<el-main class="nopadding">
				<wpTable
					ref="table"
					:apiObj="apiObj"
					:column="columns"
					row-key="id"
					stripe
					@selection-change="handleSelectionChange"
				>
				</wpTable>
			</el-main>
		</el-container>

		<!-- Modals -->
		<edit-modal
			v-if="visible"
			ref="editModal"
			@closed="closed"
			@success="getPage"
		></edit-modal>
		<detail-modal
			v-if="showDetail"
			ref="detailModal"
			@closed="closed"
		></detail-modal>
	</el-container>
</template>

<script>
import { tableColumns, listQuery, searchConfig } from "./config";
import editModal from "./edit";
import detailModal from "./detail";
export default {
	name: "DeviceRepairTable",
	components: {
		editModal,
		detailModal,
	},
	data() {
		return {
			apiObj: this.$API["om/deviceRepair"].page,
			columns: tableColumns.bind(this)(),
			searchConfig: null,
			listQuery: Object.assign({}, listQuery),
			selections: [],
			visible: false,
			showDetail: false,
			dialogVisible: false,
			// 类别树相关数据
			categoryTreeData: [],
			filterText: "",
			defaultProps: {
				children: "children",
				label: "name",
			},
		};
	},
	computed: {},
	watch: {
		filterText(val) {
			this.$refs.categoryTreeRef.filter(val);
		},
	},
	created() {
		this.searchConfig = searchConfig.bind(this)();
	},
	mounted() {
		this.getCategoryTree();
	},
	methods: {
		// 获取类别树
		async getCategoryTree() {
			try {
				const res = await this.$API[
					"dt/category"
				].getTree.get({type:"DT_DEVICE"});
				if (res.success && res.data) {
					res.data.forEach(obj=>{obj.id=null})
					this.categoryTreeData = res.data;
				} else {
					this.$message.error(
						"获取设备类别失败: " + (res.msg || "未知错误")
					);
					this.categoryTreeData = [
						{ id: null, name: "全部类别", children: [] },
					];
				}
			} catch (error) {
				console.error("Error fetching category tree:", error);
				this.$message.error("获取设备类别时出错");
				this.categoryTreeData = [
					{ id: null, name: "全部类别", children: [] },
				];
			}
		},
		filterNode(value, data) {
			if (!value) return true;
			return data.name && data.name.includes(value);
		},
		handleNodeClick(data) {
			this.listQuery.deviceId = data.id;
			this.handleSubmit();
		},
		clearCategoryFilter() {
			this.listQuery.deviceId = null;
			if (this.$refs.categoryTreeRef) {
				this.$refs.categoryTreeRef.setCurrentKey(null);
			}
			this.handleSubmit();
		},
		getPage() {
			this.$refs.table.upData(this.listQuery);
		},
		handleSubmit() {
			this.$refs.table.upData(this.listQuery);
		},
		openDetail(row) {
			this.handleOpt("detail", row);
		},
		handleSelectionChange(val) {
			this.selections = val;
		},
		async handleOpt(type, row) {
			let result = null;
			switch (type) {
				case "detail":
					this.showDetail = true;
					result = await this.$API[
						"om/deviceRepair"
					].getWithDetail.get(row.id);
					this.$nextTick(() => {
						this.$refs.detailModal.open().setData(result.data);
					});
					break;
				case "edit":
					this.visible = true;
					result = await this.$API[
						"om/deviceRepair"
					].getWithDetail.get(row.id);
					this.$nextTick(() => {
						this.$refs.editModal.open().setData(result.data);
					});
					break;
				case "delete":
					this.$confirm("确认删除吗？", "提示", {
						confirmButtonText: "确定",
						cancelButtonText: "取消",
						type: "warning",
					})
						.then(async () => {
							result = await this.$API[
								"om/deviceRepair"
							].delete.delete([row.id]);
							this.$nextTick(() => {
								if (result.success) {
									this.$message.success(result.msg || "删除");
								}
							});
							this.getPage();
						})
						.catch(() => {
							this.$message.info("已取消删除");
						});
					break;
				default:
			}
		},
		add() {
			this.visible = true;
			this.$nextTick(() => {
				this.$refs.editModal.open();
			});
		},
		closed() {
			this.visible = !this.visible;
			this.showDetail = !this.showDetail;
			this.getPage();
		},
	},
};
</script>
<style lang="scss" scoped>
@import "~@/style/tabs_fix.scss";

.el-aside {
	border-right: 1px solid #eee;
	display: flex;
	flex-direction: column;
}

.el-aside .el-header {
	line-height: normal;
	border-bottom: 1px solid #eee;
	height: auto;
	padding-bottom: 10px;
}

.menu {
	height: 100%;
	overflow-y: auto;
}

.custom-tree-node {
	flex: 1;
	display: flex;
	align-items: center;
	justify-content: space-between;
	font-size: 14px;
	padding-right: 8px;
}

.tree-header {
	width: 100%;
}

.tree-title {
	margin: 0 0 10px 0;
	font-size: 16px;
	font-weight: 600;
	color: #303133;
	text-align: center;
	padding-bottom: 8px;
	border-bottom: 1px solid #ebeef5;
}

.el-container > .el-container {
	flex-direction: column;
}
</style>
