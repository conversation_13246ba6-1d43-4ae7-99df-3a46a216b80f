<template>
	<el-container>
		<el-header>
			<div class="left-panel">
				<div class="left-panel-search">
					<wp-search-form :data="searchConfig"></wp-search-form>
				</div>
			</div>
		</el-header>
		<el-header style="padding-top: 13px">
			<el-button type="primary" @click="add">添加检测</el-button>
		</el-header>
		<el-main class="nopadding">
			<wpTable
				ref="table"
				:apiObj="apiObj"
				:column="columns"
				row-key="id"
				stripe
				@selection-change="handleSelectionChange"
			>
			</wpTable>
		</el-main>
		<edit-modal
			v-if="visible"
			ref="editModal"
			@closed="closed"
			@success="getPage"
		></edit-modal>
		<detail-modal
			v-if="showDetail"
			ref="detailModal"
			@closed="closed"
		></detail-modal>
	</el-container>
</template>

<script>
import { tableColumns, listQuery, searchConfig } from "./config";
import editModal from "./edit";
import detailModal from "./detail";
export default {
	name: "DeviceRepairTable",
	components: {
		editModal,
		detailModal,
	},
	data() {
		return {
			apiObj: this.$API["om/deviceRepair"].page,
			columns: tableColumns.bind(this)(),
			searchConfig: null,
			listQuery: Object.assign({}, listQuery),
			selections: [],
			visible: false,
			showDetail: false,
			dialogVisible: false,
		};
	},
	computed: {},
	watch: {},
	created() {
		this.searchConfig = searchConfig.bind(this)();
	},
	mounted() {},
	methods: {
		getPage() {
			this.$refs.table.upData(this.listQuery);
		},
		handleSubmit() {
			this.$refs.table.upData(this.listQuery);
		},
		openDetail(row) {
			this.handleOpt("detail", row);
		},
		handleSelectionChange(val) {
			this.selections = val;
		},
		async handleOpt(type, row) {
			let result = null;
			switch (type) {
				case "detail":
					this.showDetail = true;
					result = await this.$API[
						"om/deviceRepair"
					].getWithDetail.get(row.id);
					this.$nextTick(() => {
						this.$refs.detailModal.open().setData(result.data);
					});
					break;
				case "edit":
					this.visible = true;
					result = await this.$API[
						"om/deviceRepair"
					].getWithDetail.get(row.id);
					this.$nextTick(() => {
						this.$refs.editModal.open().setData(result.data);
					});
					break;
				case "delete":
					this.$confirm("确认删除吗？", "提示", {
						confirmButtonText: "确定",
						cancelButtonText: "取消",
						type: "warning",
					})
						.then(async () => {
							result = await this.$API[
								"om/deviceRepair"
							].delete.delete([row.id]);
							this.$nextTick(() => {
								if (result.success) {
									this.$message.success(result.msg || "删除");
								}
							});
							this.getPage();
						})
						.catch(() => {
							this.$message.info("已取消删除");
						});
					break;
				default:
			}
		},
		add() {
			this.visible = true;
			this.$nextTick(() => {
				this.$refs.editModal.open();
			});
		},
		closed() {
			this.visible = !this.visible;
			this.showDetail = !this.showDetail;
			this.getPage();
		},
	},
};
</script>
<style scoped></style>
