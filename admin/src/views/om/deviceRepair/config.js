import { h, resolveComponent } from "vue";

//组件参数数据
export const componentsData = {};
// 搜索model
export const listQuery = {
	code: null,
	result: null,
	resultDetail: null,
	deviceId: null,
	repairAt: null,
	repairBy: null,
	checkAt: null,
	checkBy: null,
};

// 搜索表单配置
export const searchConfig = function () {
	return {
		list: [
			// {
			// 	type: "selectRemote",
			// 	label: "设备名称",
			// 	model: "deviceId",
			// 	options: {
			// 		request: this.$API['dt/device-info'].deviceInfoPage.get,
			// 		items: [],
			// 		props: {
			// 			children: "children",
			// 			label: "name",
			// 		},
			// 	},
			// },

			{
				type: "input",
				label: "登记人",
				model: "checkByName",
				options: {},
			},
			{
				type: "datetimerange",
				label: "维修时间",
				model: "repairAt",
				options: {},
			},
		],
		config: {
			size: "default",
			onSearch: () => {
				this.handleSubmit();
			},
			model: this.listQuery,
		},
	};
};

// 表格展示配置
export const tableColumns = function () {
	return [
		{
			type: "checkbox",
			fixed: "center",
		},

		{
			label: "设备名称",
			prop: "deviceName",
			showOverflowTooltip: true,
			align: "left",
		},
		{
			label: "故障概述",
			prop: "result",
			showOverflowTooltip: true,
			align: "left",
		},
		{
			label: "登记时间",
			prop: "checkAt",
			align: "left",
			showOverflowTooltip: true,
		},
		{
			label: "登记人",
			prop: "checkByName",
			showOverflowTooltip: true,
			align: "left",
		},
		{
			label: "维修时间",
			prop: "repairAt",
			align: "left",
			showOverflowTooltip: true,
		},
		{
			label: "维修人",
			prop: "repairByName",
			showOverflowTooltip: true,
			align: "left",
		},
		{
			prop: "opt",
			fixed: "right",
			label: "操作",
			align: "center",
			render: (row) => {
				const optArr = [];
				const detailBtn = h(resolveComponent("wp-tip-button"), {
					content: "详情",
					type: "text",
					icon: "wp-icon-detail",
					onClick: () => {
						this.handleOpt("detail", row);
					},
				});

				const editBtn = h(resolveComponent("wp-tip-button"), {
					content: "编辑",
					type: "text",
					icon: "wp-icon-edit",
					onClick: () => {
						// 编辑
						this.handleOpt("edit", row);
					},
				});

				const delBtn = h(resolveComponent("wp-tip-button"), {
					content: "删除",
					type: "text",
					icon: "wp-icon-delete",
					onClick: () => {
						// 编辑
						this.handleOpt("delete", row);
					},
				});

				optArr.push(detailBtn);
				optArr.push(editBtn);
				optArr.push(delBtn);
				return h("div", null, optArr);
			},
		},
	];
};

// 表格展示配置
export const editConfig = function () {
	return {
		labelWidth: "130px",
		labelPosition: "top",
		size: "medium",
		formItems: [
			{
				label: "设备名称",
				name: "deviceId",
				value: "",
				component: "selectRemote",
				span: 12,
				options: {
					request: this.$API['dt/device-info'].deviceInfoPage.get,
				},
				rules: [
					{
						required: true,
						message: "请选择设备名称",
						trigger: "blur",
					},
				],
			},
			{
				label: "维修作业部门",
				name: "repairOrgId",
				component: "inputTree",
				defaultText: "repairOrgName",
				span: 12,
				options: {
					request: this.$API["uc/org"].getOrgTree.get,
					items: [],
				},
				rules: [{ required: true, message: "必填项", trigger: "blur" }],
			},
			{
				label: "维修时间",
				name: "repairAt",
				value: "",
				component: "date",
				span: 12,
				options: {
					type: "datetime",
					format: "YYYY-MM-DD HH:mm:ss",
				},
				rules: [{ required: true, message: "必填项", trigger: "blur" }],
			},
			{
				label: "维修作业人员",
				name: "repairBy",
				value: "",
				component: "selectRemote",
				span: 12,
				options: {
					queryKey: "keyword",
					request: this.$API["uc/user"].pageSimple.get,
					params: { status: "ENABLED", pageSize: 1000 },
					placeholder: "请选择",
				},
				rules: [{ required: true, message: "必填项", trigger: "blur" }],
			},
			{
				label: "登记人",
				name: "checkBy",
				value: "",
				component: "selectRemote",
				span: 12,
				options: {
					queryKey: "keyword",
					request: this.$API["uc/user"].pageSimple.get,
					params: { status: "ENABLED", pageSize: 1000 },
					placeholder: "请选择",
				},
				rules: [{ required: true, message: "必填项", trigger: "blur" }],
			},
			{
				label: "故障概述",
				name: "result",
				value: "",
				component: "input",
				span: 24,
				options: {},
				rules: [{ required: true, message: "必填项", trigger: "blur" }],
			},
			{
				label: "故障说明",
				name: "resultDetail",
				value: "",
				component: "textarea",
				span: 24,
				options: {},
			},
			{
				label: "相关图片",
				name: "fileRelList",
				value: [],
				component: "upload",
				options: {
					multiple: true,
				},
			},
		],
	};
};
