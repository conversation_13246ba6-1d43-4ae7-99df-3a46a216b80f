<template>
	<!-- 抽屉 详情 -->
	<div class="drawer">
		<el-drawer
			v-model="isShow"
			:size="widthDrawer"
			custom-class="detail-drawer"
			destroy-on-close
			@closed="$emit('closed')"
		>
			<template #header>
				<div v-if="titleDrawer" class="title">
					<div class="line"></div>
					<span>{{ titleDrawer }}</span>
				</div>
			</template>

			<template #default>
				<div class="drawer-content">
					<div class="drawer-item">
						<div class="drawer-item-form">
							<el-form
								ref="editForm"
								:model="form"
								:rules="rules"
								class="form-item"
							>
								<el-form-item
									:rules="[
										{
											required: true,
											message: '请输入名称',
											trigger: 'blur',
										},
									]"
									label="名称"
									prop="name"
								>
									<el-input
										v-model.trim="form.name"
										maxlength="50"
										show-word-limit
									></el-input>
								</el-form-item>
								<el-form-item label="状态">
									<el-radio-group v-model="form.status">
										<el-radio
											v-for="(item, index) in statusList"
											:key="index"
											:label="item.value"
											:value="item.value"
										>
											{{ item.label }}
										</el-radio>
									</el-radio-group>
								</el-form-item>
								<el-form-item label="分类排序">
									<el-input
										v-model="form.showOrder"
										:formatter="
											(value) =>
												`$ ${value}`.replace(
													/^(0+)|\D+/g,
													''
												)
										"
										:parser="
											(value) =>
												value.replace(/^(0+)|\D+/g, '')
										"
										maxlength="9"
									></el-input>
								</el-form-item>
							</el-form>
						</div>
					</div>
				</div>
			</template>

			<template #footer>
				<div class="drawer-footer">
					<div v-if="typeDrawer === 'add'" class="drawer-button">
						<el-button plain type="primary" @click="cancelClick"
							>取消新增
						</el-button>
						<el-button type="primary" @click="confirmClick"
							>确认新增
						</el-button>
					</div>
					<div v-if="typeDrawer === 'edit'" class="drawer-button">
						<el-button plain type="primary" @click="cancelClick"
							>取消
						</el-button>
						<el-button type="primary" @click="confirmClick"
							>确认
						</el-button>
					</div>
				</div>
			</template>
		</el-drawer>
	</div>
</template>

<script>
import backImgSrc from "@/assets/images/common/details_icon_back.svg";
import drawerTitle from "@/assets/images/drawer/drawer_icon_title.png";

export default {
	name: "categoryDrawer",
	props: {
		showDrawer: { type: Boolean, default: true }, // 弹出框是否展示
		widthDrawer: { type: Number, default: 560 }, // 弹出框是否展示
		categoryType: { type: String, default: "KM" }, // 仓库类型
		typeDrawer: { type: String, default: "add" }, // 弹出框是否展示
		id: { type: String, default: "" }, // 弹出框是否展示
		titleDrawer: { type: String, default: "" }, // 弹出框标题
	},
	data() {
		return {
			drawerTitle: drawerTitle,
			backImgSrc: backImgSrc,
			formCheckList: [],
			isShow: false,
			form: {
				id: "", // id
				code: "", // 编号
				name: "", // 名称
				// 仓库数量
				status: "ENABLED", // 状态
				showOrder: 0, // 分类排序
				type: "KM",
			},
			statusList: [
				{ label: "启用", value: "ENABLED" },
				{ label: "停用", value: "DISABLED" },
			],
			textarea: "", // 核对登记 备注
			rules: {
				name: [
					{ required: true, message: "名称必填", trigger: "blur" },
				],
			},
		};
	},
	created() {
		if (this.id) {
			this.form.id = this.id;
			this.getFormData(this.id);
		} else {
			this.form = {
				status: "ENABLED", // 状态
			};
		}
	},
	methods: {
		setData(data) {
			Object.assign(this.form, data);
		},
		open(mode) {
			this.isShow = true;
			return this;
		},
		getFormData(id) {
			this.form = {};
			this.$API["om/category"].getById.get(id).then((res) => {
				// console.log(res);
				this.form = res.data;
			});
		},
		// 点击 取消入库
		cancelClick() {
			this.isShow = false;
			return this;
		},
		// 点击 确认入库
		confirmClick() {
			this.$refs["editForm"].validate((valid) => {
				console.log(valid);
				this.form.type = this.categoryType.type;
				console.log(this.form.type);
				if (valid) {
					const { type, name, showOrder, status } = this.form;
					if (this.id) {
						// 编辑
						this.$API["om/category"].edit
							.post({
								type: "KM",
								name,
								showOrder,
								status,
								id: this.id,
							})
							.then((res) => {
								if (res.success) {
									this.$message.success("编辑成功");
									this.isShow = false;
									this.$emit("getPage");
								} else {
									this.$message.error(res.msg);
								}
							});
					} else {
						// 新增
						this.$API["om/category"].add
							.post({
								type: "KM",
								name,
								showOrder,
								status,
							})
							.then((res) => {
								if (res.success) {
									this.$message.success("新增成功");
									this.isShow = false;
									this.$emit("getPage");
								} else {
									this.$message.error(res.msg);
								}
							});
					}
				}
			});
		},
	},
};
</script>

<style lang="scss" scoped>
@import "~@/style/drawer.scss";
</style>
