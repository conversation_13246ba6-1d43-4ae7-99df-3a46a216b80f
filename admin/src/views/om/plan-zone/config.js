import { h, resolveComponent } from "vue";
import commonEnum from "@/config/common";

//组件参数数据
export const componentsData = {};
// 搜索model
export const listQuery = {
	code: null,
	planId: null,
	zoneId: null,
};

// 搜索表单配置
export const searchConfig = function () {
	return {
		list: [
			{
				type: "input",
				label: "编号",
				model: "code",
				options: {},
			},
			{
				type: "input",
				label: "计划主键",
				model: "planId",
				options: {},
			},
			{
				type: "input",
				label: "区域主键",
				model: "zoneId",
				options: {},
			},
		],
		config: {
			size: "default",
			onSearch: () => {
				this.handleSubmit();
			},
			model: this.listQuery,
		},
	};
};

// 表格展示配置
export const tableColumns = function () {
	return [
		{
			type: "checkbox",
			fixed: "center",
		},
		{
			label: "编号",
			prop: "code",
			showOverflowTooltip: true,
			align: "left",
			width: 120,
		},
		{
			label: "计划主键",
			prop: "planId",
			showOverflowTooltip: true,
			align: "left",
			width: 120,
		},
		{
			label: "区域主键",
			prop: "zoneId",
			showOverflowTooltip: true,
			align: "left",
			width: 120,
		},
		{
			prop: "opt",
			fixed: "right",
			label: "操作",
			align: "center",
			width: 160,
			render: (row) => {
				const optArr = [];
				const detailBtn = h(resolveComponent("wp-tip-button"), {
					content: "详情",
					type: "text",
					icon: "wp-icon-detail",
					onClick: () => {
						this.handleOpt("detail", row);
					},
				});

				const editBtn = h(resolveComponent("wp-tip-button"), {
					content: "编辑",
					type: "text",
					icon: "wp-icon-edit",
					onClick: () => {
						// 编辑
						this.handleOpt("edit", row);
					},
				});

				const delBtn = h(resolveComponent("wp-tip-button"), {
					content: "删除",
					type: "text",
					icon: "wp-icon-delete",
					onClick: () => {
						// 编辑
						this.handleOpt("delete", row);
					},
				});

				optArr.push(editBtn);
				optArr.push(delBtn);
				return h("div", null, optArr);
			},
		},
	];
};

// 表格展示配置
export const editConfig = function () {
	return {
		labelWidth: "130px",
		labelPosition: "top",
		size: "medium",
		formItems: [
			{
				label: "编号",
				name: "code",
				value: "",
				component: "input",
				span: 12,
				options: {},
				rules: [{ required: true, message: "必填项", trigger: "blur" }],
			},
			{
				label: "计划主键",
				name: "planId",
				value: "",
				component: "input",
				span: 12,
				options: {},
			},
			{
				label: "区域主键",
				name: "zoneId",
				value: "",
				component: "input",
				span: 12,
				options: {},
			},
		],
	};
};
