<template>
	<el-dialog
		append-to-body
		:title="`${title === 'ADD' ? '新增' : '编辑'}`"
		:visible.sync="visible"
		width="550px"
		:before-close="handleClose"
	>
		<el-form
			v-if="visible"
			ref="editCaForm"
			:model="editForm"
			:rules="rules"
			label-width="120px"
		>
			<section class="dialog__form__container">
				<el-form-item v-if="type === 'OBJECT'" label="上级类别">
					<ht-input-tree
						v-if="editForm.pId"
						v-model="editForm.pId"
						disabled
						:default-text="editForm.pName"
						:props="{
							child: 'child',
							label: 'name',
						}"
						:request="getObjectCategoryTree"
						:clearable="false"
						:show-all="true"
						title="选择上级类别"
					/>
				</el-form-item>

				<el-form-item v-else label="上级类别">
					<ht-input-tree
						v-if="editForm.pId"
						v-model="editForm.pId"
						disabled
						:default-text="editForm.pName"
						:props="{
							child: 'child',
							label: 'name',
						}"
						:request="getTempCategoryTree"
						:clearable="false"
						:show-all="true"
						title="选择上级类别"
					/>
				</el-form-item>

				<el-form-item
					label="类别名称"
					prop="name"
					:rules="[rules.required]"
				>
					<el-input v-model.trim="editForm.name" :maxlength="100" />
				</el-form-item>

				<el-form-item label="">
					<el-button size="small" type="primary" @click="handleOk">
						保存
					</el-button>
					<el-button size="small" @click="handleClose">
						取消
					</el-button>
				</el-form-item>
			</section>
		</el-form>
	</el-dialog>
</template>

<script>
import {
	insertCategory,
	get,
	updateCategory,
	getObjectCategoryTree,
	getTempCategoryTree,
} from "@/api/moudles/dt/dt-category";

import { rules } from "@/utils/validator.js";

export default {
	name: "CategoryEditDialog",
	props: {
		data: {
			type: Object,
			default: () => {},
		},
		title: {
			type: String,
			default: "ADD", // ADD / EDIT
		},
		type: {
			type: String,
			default: "OBJECT", // object / temp / decorate
		},
	},
	data() {
		return {
			visible: false,
			getObjectCategoryTree,
			getTempCategoryTree,
			editForm: {
				id: "",
				pId: "",
				localPrefabPath: "",
				localAssetPath: "",
				level: 1,
				name: "",
				type: "OBJECT",
			},
			rules,
			componentsData: {
				orgOptions: {},
			},
		};
	},
	watch: {
		data(val) {
			console.log(val);
			this.editForm = val;
		},
	},
	created() {},
	methods: {
		selectChange() {
			this.$forceUpdate();
		},
		handleOk() {
			console.log(this.editForm);
			if (this.$store.getters.ajaxIng) {
				return;
			}
			this.$refs.editCaForm.validate((valid) => {
				if (!valid) {
					return;
				}
				let request = null;
				if (this.title === "ADD") {
					console.log("=====", this.editForm);
					request = insertCategory(this.editForm);
				} else {
					if (this.editForm.pId === this.editForm.id) {
						this.$message.warning("请选择父类别");
						return;
					}
					request = updateCategory(this.editForm);
				}

				request.then((result) => {
					if (result.success) {
						this.$message.success(result.msg || "保存成功");
						this.visible = false;
						this.$emit("success", result);
					} else {
						this.$message.error(result.msg || "保存失败");
					}
				});
			});
		},
		handleClose() {
			this.visible = false;
			this.$refs["editCaForm"].resetFields();
		},
		open() {
			this.visible = true;
		},
	},
};
</script>
