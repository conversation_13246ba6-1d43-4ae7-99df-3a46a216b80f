// 组件参数数据
import { h, resolveComponent } from "vue";
import HtEnum from "../../../../enum/HtEnum";
import commonEnum from "@/api/moudles/om/common";

//组件参数数据
export const componentsData = {
	statusSelectList: [
		{
			label: "启用",
			value: "ENABLED",
		},
		{
			label: "停用",
			value: "DISABLED",
		},
	],
};

export const STATUS = new HtEnum([
	{
		key: "ENABLED",
		text: "启用",
	},
	{
		key: "DISABLED",
		text: "停用",
	},
]);

// 搜索表单配置
export const searchConfig = function () {
	return {
		list: [
			{
				type: "input",
				label: "设备名称",
				model: "name",
				options: {
					placeholder: "请输入名称",
				},
			},
			{
				type: "select",
				label: "设备状态",
				model: "status",
				options: {
					options: STATUS.values(),
				},
			},
		],
		config: {
			size: "default",
			onSearch: () => {
				this.handleSubmit();
			},
			model: this.listQuery,
		},
	};
};

// 表格展示配置
export const tableColumns = function () {
	return [
		{
			type: "checkbox",
			fixed: "center",
		},

		{
			label: "设备名称",
			prop: "name",
			align: "left",
			showOverflowcontent: true,
		},
		{
			label: "设备规格",
			prop: "specs",
			align: "left",
			showOverflowcontent: true,
		},
		{
			label: "设备类型",
			prop: "categoryName",
			align: "left",
			showOverflowcontent: true,
		},
		{
			label: "楼宇信息",
			prop: "buildName",
			align: "left",
			showOverflowcontent: true,
		},
		{
			label: "状态",
			prop: "status",
			align: "left",
		},
		{
			prop: "opt",
			fixed: "right",
			label: "操作",
			align: "center",
			render: (row) => {
				const optArr = [];
				const editBtn = h(resolveComponent("wp-tip-button"), {
					content: "编辑",
					type: "text",
					icon: "wp-icon-edit",
					onClick: () => {
						// 编辑
						this.handleOpt("edit", row);
					},
				});

				const settBtn = h(resolveComponent("wp-tip-button"), {
					content: "配置",
					type: "text",
					icon: "el-icon-setting",
					onClick: () => {
						// 编辑
						this.handleOpt("sett", row);
					},
				});

				const delBtn = h(resolveComponent("wp-tip-button"), {
					content: "删除",
					type: "text",
					icon: "wp-icon-delete",
					onClick: () => {
						this.handleOpt("delete", row);
					},
				});
				const disableBtn = h(resolveComponent("wp-tip-button"), {
					content: "停用",
					type: "text",
					icon: "wp-icon-deactive",
					link: true,
					onClick: () => {
						this.handleOpt("disable", row);
					},
				});

				const enableBtn = h(resolveComponent("wp-tip-button"), {
					content: "启用",
					type: "text",
					icon: "wp-icon-enable",
					onClick: () => {
						// 编辑
						this.handleOpt("enable", row);
					},
				});
				switch (row.status) {
					case "ENABLED":
						optArr.push(disableBtn);
						break;
					case "DISABLED":
						optArr.push(editBtn);
						optArr.push(enableBtn);
						optArr.push(delBtn);
						break;
					default:
				}
				optArr.push(settBtn);
				return h("div", null, optArr);
			},
		},
	];
};

// 表格展示配置
export const editConfig = function () {
	return {
		labelWidth: "130px",
		labelPosition: "top",
		size: "medium",
		formItems: [
			{
				label: "设备类型",
				name: "categoryId",
				defaultText: "categoryName",
				component: "inputTree",
				span: 12,
				options: {
					request: this.$API["om/category"].getCategoryTree.get,
					items: [],
					props: {
						children: "children",
						label: "name",
					},
				},
				rules: [{ required: true, message: "必填项", trigger: "blur" }],
			},
			{
				label: "楼宇信息",
				name: "buildId",
				defaultText: "buildName",
				component: "inputTree",
				span: 12,
				options: {
					request: this.$API["om/category"].getBuildingTree.get,
					items: [],
					disabled: true,
					props: {
						children: "children",
						label: "name",
					},
				},
				rules: [{ required: true, message: "必填项", trigger: "blur" }],
			},
			{
				label: "设备名称",
				name: "name",
				value: "",
				component: "input",
				span: 12,
				options: {},
				rules: [{ required: true, message: "必填项", trigger: "blur" }],
			},
			{
				label: "设备编号",
				name: "code",
				value: "",
				component: "input",
				span: 12,
				options: {},
			},
			{
				label: "设备规格",
				name: "specs",
				value: "",
				component: "input",
				span: 12,
				options: {},
			},
			{
				label: "维保日期",
				name: "repairAt",
				value: "",
				component: "date",
				span: 12,
				options: {
					type: "datetime",
					format: "YYYY-MM-DD HH:mm:ss",
				},
			},
			{
				label: "品牌名称",
				name: "brandName",
				value: "",
				component: "input",
				span: 12,
				options: {},
			},
			{
				label: "生产厂家",
				name: "produceName",
				value: "",
				component: "input",
				span: 12,
				options: {},
			},
			{
				label: "预警推送",
				name: "alertType",
				value: "",
				component: "radio",
				span: 12,
				options: {
					items: [...commonEnum.alertType],
				},
				rules: [{ required: true, message: "必填项", trigger: "blur" }],
			},
			{
				label: "产品介绍",
				name: "common",
				value: "",
				component: "textarea",
				options: {},
			},
			{
				label: "产品文档",
				name: "docFileRelList",
				value: [],
				component: "file",
				options: {
					multiple: true,
				},
			},
			{
				label: "操作视频",
				name: "videoFileRelList",
				value: [],
				component: "video",
				options: {
					multiple: true,
				},
			},
			{
				label: "状态",
				name: "status",
				value: "",
				component: "radio",
				span: 12,
				options: {
					items: [...componentsData.statusSelectList],
				},
			},
		],
	};
};
