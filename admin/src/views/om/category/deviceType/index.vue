<template>
	<el-container>
		<el-aside width="250px">
			<el-container>
				<el-header style="padding-top: 10px">
					<el-input
						v-model.trim="groupFilterText"
						clearable
						placeholder="输入关键字进行过滤"
					></el-input>
				</el-header>
				<el-main class="nopadding">
					<el-tree
						ref="group"
						:data="categoryTreeData"
						:expand-on-click-node="false"
						:filter-node-method="groupFilterNode"
						:highlight-current="true"
						:props="treeProps"
						class="menu"
						default-expand-all
						node-key="id"
						@node-click="nodeClick"
					></el-tree>
				</el-main>
			</el-container>
		</el-aside>
		<el-container>
			<el-header>
				<wpSearchForm :data="searchConfig"></wpSearchForm>
			</el-header>
			<el-header style="padding-top: 13px">
				<el-button type="primary" @click="handleAdd">新增</el-button>
			</el-header>
			<el-main class="nopadding">
				<wpTable
					ref="table"
					:apiObj="apiObj"
					:column="tableColumns"
					row-key="id"
					stripe
					@selection-change="selectionChange"
				>
					<template #status="scope">
						<el-tag
							v-if="scope.row.status === 'ENABLED'"
							color="#48C7A2"
							effect="dark"
							round
						>
							{{ searchStatus(scope.row.status) }}
						</el-tag>
						<el-tag
							v-if="scope.row.status === 'DISABLED'"
							color="#FF636F"
							effect="dark"
							round
						>
							{{ searchStatus(scope.row.status) }}
						</el-tag>
					</template>
				</wpTable>
			</el-main>
		</el-container>
	</el-container>

	<device-edit
		v-if="showEditDrawer"
		:showEditDrawer="showEditDrawer"
		:id="id"
		:key="timer"
		ref="editDrawer"
		:titleEditDrawer="titleEditDrawer"
		:widthDrawer="560"
		@closed="showEditDrawer = false"
		@getPage="getPage"
	>
	</device-edit>

	<device-mate-table
		v-if="showMateDrawer"
		:deviceId="deviceId"
		:key="timer"
		ref="mateDrawer"
		titleEditDrawer="设备信息"
		:widthDrawer="660"
		@closed="showMateDrawer = !showMateDrawer"
		@getPage="getPage"
	>
	</device-mate-table>
</template>

<script>
import { tableColumns, searchConfig } from "./config";
import deviceEdit from "./edit";
import deviceMateTable from "./mate/index";
import commonEnum from "@/api/moudles/om/common";

export default {
	components: {
		deviceEdit,
		deviceMateTable,
	},
	name: "AssetTable",
	data() {
		return {
			apiObj: this.$API["om/deviceInfo"].page,
			groupFilterText: "",
			selection: [],
			data: [],
			timer: "", // 组件时间戳 重新加载组件
			editType: "ADD",
			treeProps: {
				label: "name",
				id: "id",
				children: "children",
			},
			editQuery: {
				id: "",
				name: "",
				code: null,
				pId: "",
				pName: "",
			},
			showEditDrawer: false, // 点击详情 传递 是否显示
			titleEditDrawer: "", // 点击详情 传递 标题
			//mate 配置
			deviceId: "",
			showMateDrawer: false,
			tableColumns: tableColumns.bind(this)(),
			searchConfig: null,
			listQuery: {
				includeChild: true,
				name: null,
				status: null,
			},
		};
	},
	created() {
		this.searchConfig = searchConfig.bind(this)();
		this.getCategoryTree();
	},
	watch: {
		groupFilterText(val) {
			this.$refs.group.filter(val);
		},
	},
	methods: {
		upData() {
			this.getPage();
		},
		getPage() {
			this.$refs.table.upData();
		},
		// 表格选择后回调事件
		selectionChange(selection) {
			this.selection = selection;
		},
		handleSubmit() {
			this.$refs.table.upData(this.listQuery);
		},
		// 树过滤
		groupFilterNode(value, data) {
			if (!value) return true;
			return data.name && data.name.indexOf(value) !== -1;
		},
		nodeClick(data) {
			this.listQuery.categoryId = data.id;
			this.listQuery.categoryName = data.name;
			this.listQuery.pId = data.pId;
			this.$refs.table.upData(this.listQuery);
		},
		includeChildChange(flag) {
			this.listQuery.includeChild = flag;
			this.$refs.table.upData(this.listQuery);
		},

		async getCategoryTree() {
			try {
				const res = await this.$API[
					"dt/category"
					].getTree.get({type:"DT_DEVICE"});
				if (res.success && res.data) {
					res.data.forEach(obj=>{obj.id=null})
					this.categoryTreeData = res.data;
				} else {
					this.$message.error(
						"获取设备类别失败: " + (res.msg || "未知错误")
					);
					this.categoryTreeData = [
						{ id: null, name: "全部类别", children: [] },
					];
				}
			} catch (error) {
				console.error("Error fetching category tree:", error);
				this.$message.error("获取设备类别时出错");
				this.categoryTreeData = [
					{ id: null, name: "全部类别", children: [] },
				];
			}
		},

		handleSearch() {
			this.$refs.table.upData(this.listQuery);
		},

		handleAdd() {
			this.id = "";
			this.showEditDrawer = true;
			this.titleEditDrawer = "设备录入";
			this.timer = new Date().getTime();
			this.$nextTick(() => {
				console.log(this.listQuery, "listQuery======");
				this.$refs.editDrawer.open(this.listQuery);
			});
		},

		async handleOpt(type, params) {
			let res = null;
			switch (type) {
				case "delete":
					this.$confirm("确认删除吗？", "提示", {
						confirmButtonText: "确定",
						cancelButtonText: "取消",
						type: "warning",
					})
						.then(() => {
							this.$API["om/deviceInfo"].delete
								.delete([params.id])
								.then((res) => {
									if (res.success) {
										this.$message.success("删除成功");
										this.getPage();
									} else {
										this.$message.error(
											res.msg || "删除失败"
										);
									}
								});
						})
						.catch(() => {
							this.$message.info("已取消删除");
						});

					break;
				case "edit":
					this.showEditDrawer = true;
					res = await this.$API["om/deviceInfo"].getWithDetail.get(
						params.id
					);
					this.$nextTick(() => {
						this.$refs.editDrawer.open().setData(res.data);
					});

					break;
				case "sett":
					this.deviceId = params.id;
					this.showMateDrawer = true;
					this.timer = new Date().getTime();
					this.$nextTick(() => {
						this.$refs.mateDrawer.open();
					});
					break;
				case "disable":
					this.$API["om/deviceInfo"].suspend
						.post({ id: params.id })
						.then((res) => {
							if (res.success) {
								this.$message.success(res.msg || "操作成功");
								this.getPage();
							} else {
								this.$message.error(res.msg || "操作失败");
							}
						});
					break;
				case "enable":
					this.$API["om/deviceInfo"].enable
						.post({ id: params.id })
						.then((res) => {
							if (res.success) {
								this.$message.success(res.msg || "操作成功");
								this.getPage();
							} else {
								this.$message.error(res.msg || "操作失败");
							}
						});
					break;
				default:
			}
		},
		operate(request, isReloadList) {
			if (!request) {
				return;
			}
			request.then((res) => {
				if (res.success || (res.data && res.data.success)) {
					this.$message.success("操作成功");
					if (isReloadList) {
						this.getPage();
					}
				} else {
					this.$message.error("操作失败！");
				}
			});
		},
		searchStatus(status) {
			return commonEnum.searchDeviceStatus(status);
		},
	},
};
</script>
