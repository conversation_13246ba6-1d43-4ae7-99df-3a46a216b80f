<template>
	<div>
		<el-dialog
			v-model="dialogVisible"
			:title="title"
			custom-class="dialog"
			width="500px"
		>
			<div
				class="out-flex-container back-detail"
				style="padding-right: 10px"
			>
				<!--						<div class="info">-->
				<el-image
					:initial-index="4"
					:preview-src-list="[solutionImageSrc(form.reagentFileId)]"
					:src="solutionImageSrc(form.reagentFileId)"
					alt="点击查看大图"
					fit="cover"
					preview-teleported
					style="width: 64px; height: 64px"
					title="点击查看大图"
				>
					<template #error>
						<el-image
							:src="$imageDefault"
							style="width: 64px; height: 64px"
						></el-image>
					</template>
				</el-image>
				<div class="title-container">
					<div class="title">{{ form.reagentName }}</div>
					<div class="subtitle">{{ form.alias }}</div>
				</div>
				<!--						</div>-->
				<div class="inner-flex-container">
					申请量：{{ form.applyCapacity }}{{ form.reagentUnitStr }}
				</div>
			</div>
			<div style="text-align: right">
				<el-button class="to-detail" type="text" @click="toDetail">
					<span>查看出库详情</span>
					<span class="to-detail-img"> </span>
				</el-button>
			</div>

			<template #footer>
				<div class="dialog-footer">
					<el-button @click="cancel">取消</el-button>
					<el-button type="primary" @click="confirm">签收 </el-button>
				</div>
			</template>
		</el-dialog>
	</div>
</template>

<script>
import uploadConfig from "@/config/upload";

export default {
	props: {
		title: {
			type: String,
			required: true,
		},
	},
	name: "confirmDialog",
	data() {
		return {
			form: {
				reagentFileId: "",
				reagentName: "",
				alias: "",
				applyCapacity: "",
				reagentUnitStr: "",
			},
			dialogVisible: false,
		};
	},
	methods: {
		async confirm() {
			console.log(this.form);
			const res = await this.$API["rmv/stock-out"].confirmReceive.post(
				this.form.id
			);
			if (res.success) {
				this.$message.success("签收成功");
				this.dialogVisible = false;
				this.$emit("closed");
			} else {
				this.$message.error(res.msg);
			}
		},
		cancel() {
			this.dialogVisible = false;
			this.$emit("closed");
		},
		solutionImageSrc(fileId) {
			return uploadConfig.solutionImageSrc(fileId);
		},
		open() {
			this.dialogVisible = true;
			return this;
		},
		setData(data) {
			Object.assign(this.form, data);
		},
		toDetail() {
			this.$emit("showInfo", this);
		},
	},
};
</script>

<style lang="scss" scoped>
.out-flex-container {
	display: flex;
	justify-content: space-between;
	width: 100%;

	&.back-detail {
		align-items: center;
		background-color: #f8fafa;
		padding: 5px;

		.title-container {
			width: 210px;
			margin-left: 5px;
			display: flex;
			flex-direction: column;
			font-size: 14px;

			.title {
				height: auto !important;
				background: none;
				border: none;
			}

			.subtitle {
				font-size: 12px;
			}
		}

		.inner-flex-container {
			text-align: right;
			width: calc(100% - 280px);
		}
	}
}
</style>
