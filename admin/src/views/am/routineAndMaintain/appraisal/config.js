import {h, resolveComponent} from "vue";
// 搜索model
export const listQuery = {
	categoryId: "", // 资产类别ID
	assetName: "", // 资产名称
	assetCode: "", // 资产编号
	planAt: "", // 计划检定日期范围
	status: "", // 计划状态
};

// 搜索表单配置
export const searchConfig = function () {
	return {
		list: [
			{
				type: "inputTree",
				label: "资产类别",
				model: "categoryId",
				options: {
					request: this.$API["am/category"].getAssetCategoryTree.get,
					items: [],
					props: {
						children: "children",
						label: "name"
					},
					params: { type: "AC" },
					includeSelf: false
				},
			},
			{
				type: "input",
				label: "资产名称",
				model: "assetName",
				options: {
					placeholder: "请输入资产名称",
				},
			},
			{
				type: "input",
				label: "资产编号",
				model: "assetCode",
				options: {
					placeholder: "请输入资产编号",
				},
			},
			{
				type: "datetimerange",
				label: "计划检定日期",
				model: "planAt",
				options: {},
			},
			{
				type: "select",
				label: "计划状态",
				model: "status",
				options: {
					// TODO: 确认检定计划的状态枚举，这里使用示例状态
					options: [
						{ label: "待检定", value: "UNCHECK" },
						{ label: "已检定", value: "CHECKED" },
						// 根据实际业务可能有其他状态，如逾期、取消等
					],
				},
			},
		],
		config: {
			size: "default",
			onSearch: () => {
				this.handleSubmit();
			},
			model: this.listQuery,
		},
	};
};

// 表格展示配置
export const tableColumns = function () {
	return [
		{
			type: "selection",
			width: 50,
		},
		{
			prop: "code",
			label: "检定编号",
			width: 150,
			align: "left",
			showOverflowTooltip: true,
		},
		{
			prop: "assetCode", // 资产编号
			label: "资产编号",
			width: 150,
			align: "left",
			showOverflowTooltip: true,
		},
		{
			prop: "assetName", // 资产名称
			label: "资产名称",
			width: 150,
			align: "left",
			showOverflowTooltip: true,
		},
		{
			prop: "assetCategoryName", // 资产类别名称
			label: "资产类别",
			width: 120,
			align: "left",
			showOverflowTooltip: true,
		},
		{
			prop: "planAt",
			label: "计划检定日期",
			width: 160,
			align: "left",
			showOverflowTooltip: true,
		},
		{
			prop: "detectAt", // 实际检定日期
			label: "实际检定日期",
			width: 160,
			align: "left",
			showOverflowTooltip: true,
		},
		{
			prop: "detectByName", // 检定人姓名
			label: "检定人",
			width: 120,
			align: "left",
			showOverflowTooltip: true,
		},
		{
			prop: "status",
			label: "状态",
			width: 100,
			align: "left",
			showOverflowTooltip: true,
			render: (row) => {
				const statusMap = {
					UNCHECK: { color: "#F56C6C", label: "待检定" }, // 示例颜色
					CHECKED: { color: "#67C23A", label: "已检定" }, // 示例颜色
					// TODO: 根据实际状态定义颜色和标签
				};
				const status = statusMap[row.status] || { color: "#A0A0A0", label: row.status || "未知" };
				return h(
					resolveComponent("el-tag"),
					{ color: status.color, effect: "dark", round: true },
					status.label
				);
			},
		},
		{
			prop: "recycle",
			label: "是否循环",
			width: 100,
			align: "left",
			showOverflowTooltip: true,
			render: (row) => {
				const statusMap = {
					false: { color: "#F56C6C", label: "否" }, // 示例颜色
					true: { color: "#67C23A", label: "是" }, // 示例颜色
				};
				const status = statusMap[row.recycle] || { color: "#A0A0A0", label: row.recycle || "未知" };
				return h(
					resolveComponent("el-tag"),
					{ color: status.color, effect: "dark", round: true },
					status.label
				);
			},
		},
		{
			prop: "result", // 检定结果
			label: "检定结果",
			width: 100,
			align: "left",
			showOverflowTooltip: true,
			render: (row) => {
				// TODO: 确认检定结果的枚举和转换
				const resultText = row.result === 'QUALIFIED' ? '合格' : (row.result === 'UNQUALIFIED' ? '不合格' : (row.result || ''));
				const resultColor = row.result === 'QUALIFIED' ? '#67C23A' : (row.result === 'UNQUALIFIED' ? '#F56C6C' : '');
				return h(
					'span',
					{ style: { color: resultColor } },
					resultText
				);
			},
		},
		{
			prop: "opt",
			fixed: "right",
			label: "操作",
			align: "center",
			width: 160,
			render: (row) => {
				const optArr = [];
				const detailBtn = h(resolveComponent("wp-tip-button"), {
					content: "查看详情",
					type: "text",
					icon: "wp-icon-detail",
					onClick: () => {
						this.handleOpt("detail", row);
					},
				});

				// 只有待检定的计划才能进行检定作业
				if (row.status === 'UNCHECK') {
					const startAppraisalBtn = h(resolveComponent("wp-tip-button"), {
						content: "开始检定",
						type: "text",
						icon: "wp-icon-add-record", // 使用合适的图标
						onClick: () => {
							this.handleOpt("startAppraisal", row);
						},
					});
					optArr.push(startAppraisalBtn);
				}

				const deleteBtn = h(resolveComponent("wp-tip-button"), {
					content: "删除",
					type: "text",
					icon: "wp-icon-delete",
					onClick: () => {
						this.handleOpt("delete", row);
					},
				});

				optArr.push(detailBtn);
				optArr.push(deleteBtn);

				return h("div", null, optArr);
			},
		},
	];
};
