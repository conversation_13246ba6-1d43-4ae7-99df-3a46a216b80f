<template>
	<el-container>
		<el-header>
			<div class="left-panel">
				<div class="left-panel-search">
					<wp-search-form :data="searchConfig"></wp-search-form>
				</div>
			</div>
		</el-header>
		<el-header style="padding-top: 13px">
			<!-- 添加检定计划通常是选择资产后创建 -->
			<el-button type="primary" @click="handleOpt('addPlan')">添加检定计划</el-button>
		</el-header>
		<el-main class="nopadding">
			<wpTable
				ref="table"
				:apiObj="list.apiObj"
				:column="columns"
				row-key="id"
				stripe
				@selection-change="handleSelectionChange"
			>
			</wpTable>
		</el-main>
		<!-- edit.vue 用于记录检定作业 -->
		<edit-modal
			v-if="visible"
			ref="EditModal"
			@closed="handleSubmit"
			@success="handleSubmit"
		></edit-modal>
		<!-- detail.vue 用于查看检定计划详情 -->
		<detail-modal
			v-if="detailShow"
			ref="DetailModal"
			@closed="handleSubmit"
		></detail-modal>
		<!-- select/index.vue 用于选择资产创建计划 -->
		<select-index
			v-if="showSelectAsset"
			ref="SelectIndex"
			@closed="handleSubmit"
		>
		</select-index>
	</el-container>
</template>

<script>
import { listQuery, searchConfig, tableColumns } from "./config";
import EditModal from "./edit"; // 检定作业录入 modal
import DetailModal from "./detail"; // 检定计划详情 modal
import SelectIndex from "./select/index"; // 选择资产 modal
// import commonEnum from "@/api/moudles/dm/common"; // 如果config中需要，可能要引入

export default {
	components: {
		EditModal,
		DetailModal,
		SelectIndex,
	},
	name: "assetAppraisalPlan", // 修改组件名
	data() {
		return {
			// API 对象指向检定计划的分页接口
			list: {
				apiObj: this.$API["am/assetAppraisal"].page, // TODO: 确认实际的API路径
			},
			// 表格列配置，从 config.js 导入并绑定上下文
			columns: tableColumns.bind(this)(),
			// 搜索模型，从 config.js 导入并复制一份
			listQuery: Object.assign({}, listQuery),
			// 选中的行数据
			multipleSelection: [], // 命名改为 multipleSelection 与其他页面一致
			// 控制检定作业录入 modal 的显示
			visible: false,
			// 控制检定计划详情 modal 的显示
			detailShow: false,
			// 控制选择资产 modal 的显示
			showSelectAsset: false,
			// 搜索表单配置，从 config.js 导入并绑定上下文
			searchConfig: null,
		};
	},
	methods: {
		// 刷新表格数据，通常在关闭 modal 后调用
		handleSubmit() {
			this.visible = false; // 关闭检定作业录入 modal
			this.detailShow = false; // 关闭检定计划详情 modal
			this.showSelectAsset = false; // 关闭选择资产 modal
			this.$refs.table.upData(this.listQuery); // 刷新表格
		},
		// 处理表格行选中变化
		handleSelectionChange(val) {
			this.multipleSelection = val;
		},
		// 处理操作列按钮点击事件
		async handleOpt(type, row) {
			let result = {};
			switch (type) {
				case "addPlan": // 添加检定计划
					this.showSelectAsset = true;
					this.$nextTick(() => {
						// SelectIndex 组件应该用于选择资产，然后在其内部或关闭时触发计划创建
						this.$refs.SelectIndex.open(); // TODO: 确认SelectIndex的open方法和数据传递方式
					});
					break;
				case "detail": // 查看检定计划详情
					this.detailShow = true;
					// 获取检定计划详情数据
					result = await this.$API["am/assetAppraisal"].getById.get(row.id); // TODO: 确认实际的API路径
					this.$nextTick(() => {
						if (result.success) {
							// 将获取到的数据传递给详情 modal
							this.$refs.DetailModal.open().setData(result.data);
						} else {
							this.$message.error(result.msg || "获取详情失败");
							this.detailShow = false; // 获取失败则关闭modal
						}
					});
					break;
				case "startAppraisal": // 开始检定作业
					this.visible = true; // 显示检定作业录入 modal
					this.$nextTick(() => {
						// 将当前的检定计划数据传递给检定作业 modal
						this.$refs.EditModal.open().setData(row); // EditModal用于记录作业结果
					});
					break;
				case "delete": // 删除检定计划
					this.$confirm("确认删除该检定计划吗?", "提示", {
						confirmButtonText: "确定",
						cancelButtonText: "取消",
						type: "warning",
					})
						.then(async () => {
							// 调用删除检定计划的API
							result = await this.$API["am/assetAppraisal"].delete.delete([row.id]); // TODO: 确认实际的API路径
							if (result.success) {
								this.$message.success(result.msg || "删除成功");
								this.handleSubmit(); // 刷新列表
							} else {
								this.$message.error(result.msg || "删除失败");
							}
						})
						.catch(() => {
							this.$message.info("已取消删除");
						});
					break;
				default:
			}
		},
		// config.js 中 render 函数需要用到的方法
		searchStatus(status) {
			// TODO: 实现状态的转换逻辑，可能需要引入 commonEnum 或直接在config中定义map
			const statusMap = {
				UNCHECK: "待检定",
				CHECKED: "已检定",
				// ...其他状态
			};
			return statusMap[status] || status;
		},
		searchResult(result) {
			// TODO: 实现结果的转换逻辑，可能需要引入 commonEnum 或直接在config中定义map
			const resultMap = {
				QUALIFIED: "合格",
				UNQUALIFIED: "不合格",
				// ...其他结果
			};
			return resultMap[result] || result;
		}
	},
	created() {
		// 绑定上下文到搜索配置和表格列配置
		this.searchConfig = searchConfig.bind(this)();
		// this.columns = tableColumns.bind(this)(); // 已经在 data 中绑定
	},
};
</script>

<style lang="scss" scoped>
@import "~@/style/dialog.scss"; // 保持样式引用
</style>
