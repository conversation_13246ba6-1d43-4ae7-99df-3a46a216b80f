<template>
	<div class="drawer detail">
		<el-drawer
			v-model="visible"
			:size="640"
			custom-class="detail-drawer"
			destroy-on-close
			@closed="$emit('closed')"
		>
			<template #header>
				<div class="title">
					<div class="line"></div>
					<span>资产检定作业录入</span> <!-- 修改标题 -->
				</div>
			</template>
			<template #default>
				<div class="drawer-content">
					<el-form class="form-item" label-width="90px">
						<div class="section-title">
							<div class="section-title-bar"></div>
							<span>关联资产信息</span>
						</div>
						<!-- 关联资产信息，同 detail.vue -->
						<div class="asset-info-card" v-if="assetInfo && assetInfo.id">
							<wp-image
								:file="assetInfo.imageFileRelList[0].fileId"
								:errorImage="require(`@/assets/images/common/img_instrument_default.svg`)"
								class="asset-info-img"
							></wp-image>
							<div class="asset-info-main">
								<div class="asset-info-row">
									<span class="asset-info-label">资产编号</span>
									<span class="asset-info-value">{{ assetInfo.code }}</span>
								</div>
								<div class="asset-info-row">
									<span class="asset-info-label">资产名称</span>
									<span class="asset-info-value">{{ assetInfo.name }}</span>
								</div>
								<div class="asset-info-row">
									<span class="asset-info-label">资产类别</span>
									<span class="asset-info-value">{{ assetInfo.categoryName }}</span>
								</div>
								<div class="asset-info-row">
									<span class="asset-info-label">规格型号</span>
									<span class="asset-info-value">{{ assetInfo.specs }}</span>
								</div>
								<div class="asset-info-row">
									<span class="asset-info-label">数量</span>
									<span class="asset-info-value">{{ assetInfo.quantity }}</span>
								</div>
								<div class="asset-info-row">
									<span class="asset-info-label">单价</span>
									<span class="asset-info-value">{{ assetInfo.unitPrice }}</span>
								</div>
								<div class="asset-info-row">
									<span class="asset-info-label">总价</span>
									<span class="asset-info-value">{{ assetInfo.totalPrice }}</span>
								</div>
								<div class="asset-info-row">
									<span class="asset-info-label">供应商</span>
									<span class="asset-info-value">{{ assetInfo.supplierName }}</span>
								</div>
								<div class="asset-info-row">
									<span class="asset-info-label">购买日期</span>
									<span class="asset-info-value">{{ assetInfo.purchaseAt }}</span>
								</div>
								<div class="asset-info-row">
									<span class="asset-info-label">所属部门</span>
									<span class="asset-info-value">{{ assetInfo.belongDeptName }}</span>
								</div>
								<div class="asset-info-row">
									<span class="asset-info-label">使用部门</span>
									<span class="asset-info-value">{{ assetInfo.useDeptName }}</span>
								</div>
								<div class="asset-info-row">
									<span class="asset-info-label">出厂日期</span>
									<span class="asset-info-value">{{ assetInfo.produceAt }}</span>
								</div>
								<div class="asset-info-row">
									<span class="asset-info-label">出厂编号</span>
									<span class="asset-info-value">{{ assetInfo.produceCode }}</span>
								</div>
								<div class="asset-info-row">
									<span class="asset-info-label">制造商</span>
									<span class="asset-info-value">{{ assetInfo.produceName }}</span>
								</div>
								<div class="asset-info-row">
									<span class="asset-info-label">国别</span>
									<span class="asset-info-value">{{ assetInfo.countriesArea }}</span>
								</div>
							</div>
						</div>

						<el-divider></el-divider>

						<div class="section-title">
							<div class="section-title-bar"></div>
							<span>检定作业信息</span>
						</div>
						<div class="form-card">
							<el-form
								:model="form"
								class="form-item"
								label-width="110px"
								:rules="rules"
								ref="form"
							>
								<!-- ...表单项... -->
								<el-form-item label="检定编号">
									{{ form.code }}
								</el-form-item>
								<el-form-item label="计划检定日期">
									{{ form.planAt }}
								</el-form-item>
								<el-form-item label="是否循环">
									<el-switch
										v-model="form.recycle"
										active-text="是"
										inactive-text="否"
										:active-value="true"
										:inactive-value="false"
									/>
								</el-form-item>
								<el-form-item label="实际检定日期" prop="detectAt">
									<el-date-picker
										v-model="form.detectAt"
										:disabled-date="validateDate"
										type="datetime"
										value-format="YYYY-MM-DD HH:mm:ss"
										placeholder="请选择实际检定日期"
									/>
								</el-form-item>
								<el-form-item label="检定部门" prop="detectDeptId">
									<wp-input-tree
										v-model="form.detectDeptId"
										:clearable="false"
										:default-text="form.detectDeptName"
										:props="{ children: 'child', label: 'name' }"
										:request="this.$API['uc/org'].getOrgTree.get"
										:show-all="true"
										title="选择检定部门"
										placeholder="请选择检定部门"
									/>
								</el-form-item>
								<el-form-item label="检定人员" prop="detectBy">
									<wp-select-remote
										v-model="form.detectBy"
										:multiple="false"
										:params="{ status: 'ENABLED', pageSize: 1000 }"
										placeholder="请选择检定人员"
										:queryKey="'keyword'"
										:request="this.$API['uc/user'].pageSimple.get"
										style="width: 100%"
									>
									</wp-select-remote>
								</el-form-item>
								<el-form-item label="检定结果" prop="result">
									<el-select v-model="form.result" placeholder="请选择检定结果">
										<el-option label="合格" value="QUALIFIED"></el-option>
										<el-option label="不合格" value="UNQUALIFIED"></el-option>
									</el-select>
								</el-form-item>
								<el-form-item label="检定备注" prop="common">
									<el-input
										v-model="form.common"
										maxlength="500"
										placeholder="请输入检定备注"
										show-word-limit
										type="textarea"
									></el-input>
								</el-form-item>
								<el-form-item label="作业图片" prop="examineImageFileList">
									<wp-upload-multiple
										v-model="form.examineImageFileList"
										:limit="3"
										draggable
										tip="最多上传3个文件,单个文件不要超过10M,请上传图像格式文件"
									></wp-upload-multiple>
								</el-form-item>
								<el-form-item label="相关附件" prop="examineAttrFileList">
									<wp-upload-file
										v-model="form.examineAttrFileList"
										:limit="3"
										tip="最多上传3个文件,单个文件不要超过10M,请上传xlsx/docx格式文件"
									>
										<el-button icon="el-icon-upload" type="primary">上传附件</el-button>
									</wp-upload-file>
								</el-form-item>
							</el-form>
						</div>
					</el-form>
				</div>
			</template>
			<template #footer>
				<div class="drawer-footer">
					<div class="drawer-button">
						<el-button type="primary" @click="handleSubmit">提交</el-button>
						<el-button @click="closed">取消</el-button>
					</div>
				</div>
			</template>
		</el-drawer>
	</div>
	<!-- 检定结果不合格可能需要跳转到报废申请页面，这里暂时保留 -->
	<!-- <scrap-modal
		v-if="showScrap"
		ref="ScrapModal"
		@closed="closed"
	></scrap-modal> -->
</template>

<script>
import commonEnum from "@/api/moudles/dm/common"; // 假设commonEnum包含国别等映射
// import ScrapModal from "./scrapEdit"; // 如果需要报废申请，引入此组件

export default {
	// components: { ScrapModal }, // 如果需要报废申请，注册此组件
	name: "assetAppraisalEdit", // 修改组件名
	data() {
		return {
			// form 数据对应需要提交的检定作业信息
			form: {
				id: "", // 检定计划的ID
				assetId: "", // 关联资产ID
				code: "", // 检定编号
				planAt: "", // 计划检定日期
				recycle:"",
				detectAt: "", // 实际检定日期
				detectBy: "", // 检定人ID
				detectDeptId: "", // 检定部门ID
				detectDeptName: "", // 检定部门名称 (用于回显 wp-input-tree)
				result: "", // 检定结果
				common: "", // 检定备注

				examineImageFileList: [], // 作业图片列表 (命名与detail保持一致)
				examineAttrFileList: [], // 相关附件列表 (命名与detail保持一致)

				// 以下字段可能不需要在此处提交，但从检定计划记录中获取
				// period: null, // 检定周期
				// periodUnit: "", // 检定周期单位
			},
			// 关联资产的基本信息
			assetInfo: {
				id: "",
				code: "",
				name: "",
				categoryName: "",
				specs: "",
				quantity: null,
				unitPrice: null,
				totalPrice: null,
				supplierName: "",
				purchaseAt: "",
				belongDeptName: "",
				useDeptName: "",
				status: "",
				produceAt: "",
				produceCode: "",
				produceName: "",
				countriesArea: "",
				fileId: null,
				imageFileRelList: []
			},
			// showScrap: false, // 控制报废申请 modal
			visible: false,
			drawerTitle: require("@/assets/images/drawer/drawer_icon_title.png"), // 保持图片路径

			// 表单校验规则
			rules: {
				detectAt: [
					{required: true, message: "请选择实际检定日期", trigger: "change"},
					{validator: this.checkDetectDate, trigger: "change"}, // 自定义日期校验
				],
				detectDeptId: [
					{required: true, message: "请选择检定部门", trigger: "change"},
				],
				detectBy: [
					{required: true, message: "请选择检定人员", trigger: "change"}, // select-remote 触发change或blur
				],
				result: [
					{required: true, message: "请选择检定结果", trigger: "change"},
				],
				examineImageFileList: [
					// 根据业务需求决定图片是否必须
					// { required: true, message: "请选择作业图片", trigger: "change" },
				],
			},
		};
	},
	methods: {
		open() {
			this.visible = true;
			return this;
		},

		// 设置表单数据，data 是 index.vue 中传递过来的检定计划记录
		async setData(data) {
			// 将检定计划记录的部分信息赋值给 form，特别是 id 和关联信息
			this.form.id = data.id;
			this.form.assetId = data.assetId;
			this.form.code = data.code;
			this.form.planAt = data.planAt;

			// 如果是编辑已有的检定作业记录，需要回显数据
			if (data.detectAt) this.form.detectAt = data.detectAt;
			if (data.recycle) this.form.recycle = data.recycle;
			if (data.detectBy) this.form.detectBy = data.detectBy;
			if (data.detectDeptId) this.form.detectDeptId = data.detectDeptId;
			if (data.detectDeptName) this.form.detectDeptName = data.detectDeptName;
			if (data.result) this.form.result = data.result;
			if (data.common) this.form.common = data.common;
			if (data.examineImageFileList) this.form.examineImageFileList = data.examineImageFileList;
			if (data.examineAttrFileList) this.form.examineAttrFileList = data.examineAttrFileList;


			// 根据检定计划中的 assetId 获取关联资产的详细信息
			if (data.assetId) {
				const assetRes = await this.$API["am/assetInfo"].getWithDetail.get(data.assetId); // TODO: 确认获取资产详情的API
				if (assetRes.success) {
					Object.assign(this.assetInfo, assetRes.data);
					// 处理资产图片的 fileId
					if (this.assetInfo.imageFileRelList && this.assetInfo.imageFileRelList.length > 0) {
						this.assetInfo.fileId = this.assetInfo.imageFileRelList[0].fileId;
					}
					// 处理国别显示
					if (this.assetInfo.countriesArea) {
						this.assetInfo.countriesArea = commonEnum.searchKeys(
							commonEnum.countriesArea,
							this.assetInfo.countriesArea
						);
					}
				} else {
					this.$message.error(assetRes.msg || "获取关联资产信息失败");
				}
			} else {
				this.assetInfo = {}; // 如果没有 assetId，清空资产信息
			}
		},

		// 提交检定作业结果
		async handleSubmit() {
			this.$refs.form.validate(async (valid) => {
				if (!valid) {
					return;
				}
				// 准备提交的数据，只需要 form 中的字段
				const submitData = {...this.form};
				// TODO: 根据实际后端API要求，可能需要调整提交的数据结构或字段名
				// 例如，如果后端只需要检定记录ID和其他作业字段，可能不需要 assetId 等

				let res;
				// 调用提交检定作业结果的API
				res = await this.$API["am/assetAppraisal"].complete.post(submitData); // TODO: 确认实际的API路径和请求方法

				if (res.success) {
					this.$message.success(res.msg || "操作成功");
					this.$emit("success"); // 通知父组件操作成功，以便刷新列表
					this.closed();
				} else {
					this.$message.error(res.msg || "操作失败");
				}
			});
		},

		closed() {
			// 重置表单和数据
			this.$refs.form.resetFields();
			this.form = {
				id: "",
				assetId: "",
				code: "",
				planAt: "",
				recycle: "",
				detectAt: "",
				detectBy: "",
				detectDeptId: "",
				detectDeptName: "",
				result: "",
				common: "",
				examineImageFileList: [],
				examineAttrFileList: []
			};
			this.assetInfo = {
				id: "",
				code: "",
				name: "",
				categoryName: "",
				specs: "",
				quantity: null,
				unitPrice: null,
				totalPrice: null,
				supplierName: "",
				purchaseAt: "",
				belongDeptName: "",
				useDeptName: "",
				status: "",
				produceAt: "",
				produceCode: "",
				produceName: "",
				countriesArea: "",
				fileId: null,
				imageFileRelList: []
			};
			this.visible = false;
			this.$emit("closed"); // 通知父组件已关闭
		},

		// 自定义校验规则：实际检定日期不能晚于当前日期
		validateDate(time) {
			return time.getTime() > Date.now();
		},
		// 自定义校验规则：实际检定日期不能早于计划检定日期 (如果需要这个限制)
		// checkDetectDate(rule, value, callback) {
		//     if (!value) {
		//         callback(new Error("请选择实际检定日期"));
		//     } else if (this.form.planAt && new Date(value).getTime() < new Date(this.form.planAt).getTime()) {
		//         callback(new Error("实际检定日期不能早于计划检定日期"));
		//     } else {
		//         callback();
		//     }
		// }
		// 根据实际需求，可能只需要校验不晚于当前日期
		checkDetectDate(rule, value, callback) {
			if (!value) {
				callback(new Error("请选择实际检定日期"));
			} else if (new Date(value).getTime() > Date.now()) {
				callback(new Error("实际检定日期不能晚于当前日期"));
			} else {
				callback();
			}
		}
	},
};
</script>

<style lang="scss" scoped>
.content {
	padding: 20px;
}

.out-flex-container {
	margin-bottom: 10px;
}

.inner-flex-container {
	justify-content: space-around;
}
.form-card {
	background: #fafbfc;
	border-radius: 10px;
	box-shadow: 0 2px 8px #0001;
	padding: 18px 20px 10px 20px;
	margin-bottom: 18px;
	margin-top: 0;
}
.form-item .el-form-item {
	border-bottom: 1px solid #f0f0f0;
	margin-bottom: 0;
	padding: 8px 0;
}
.form-item .el-form-item:last-child {
	border-bottom: none;
}
.form-item .el-form-item__label {
	color: #666;
	font-weight: 500;
	font-size: 14px;
}
.form-item .el-form-item__content {
	font-size: 14px;
}
.section-title {
	display: flex;
	align-items: center;
	font-size: 16px;
	font-weight: bold;
	margin: 18px 0 12px 0;
	padding-left: 0;
}

.section-title-bar {
	width: 4px;
	height: 20px;
	background: #409eff;
	border-radius: 2px;
	margin-right: 8px;
}

.asset-info-card {
	background: #fafbfc;
	border-radius: 10px;
	box-shadow: 0 2px 8px #0001;
	padding: 18px 20px 10px 20px;
	margin-bottom: 18px;
	display: flex;
	flex-direction: row;
	align-items: flex-start;
}

.asset-info-img {
	width: 72px;
	height: 72px;
	border-radius: 8px;
	object-fit: cover;
	margin-right: 18px;
	background: #f0f0f0;
	border: 1px solid #eee;
}

.asset-info-main {
	flex: 1;
	min-width: 0;
}

.asset-info-row {
	display: flex;
	align-items: center;
	min-height: 28px;
	border-bottom: 1px solid #f0f0f0;
	padding: 2px 0;
}

.asset-info-label {
	width: 80px;
	color: #666;
	font-weight: 500;
	text-align: right;
	margin-right: 10px;
	flex-shrink: 0;
	font-size: 14px;
}

.asset-info-value {
	flex: 1;
	color: #222;
	font-size: 14px;
	word-break: break-all;
}
</style>
