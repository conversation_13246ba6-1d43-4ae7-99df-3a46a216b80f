<template>
	<div class="drawer detail">
		<el-drawer
			v-model="visible"
			:size="640"
			custom-class="detail-drawer"
			destroy-on-close
			@closed="$emit('closed')"
		>
			<template #header>
				<div class="title">
					<div class="line"></div>
					<span>资产检定计划</span>
				</div>
			</template>
			<template #default>
				<div class="drawer-content">
					<!-- 资产基本信息展示 -->
					<!-- 资产基本信息展示，表格风格 -->
					<div class="asset-info-card">
			  <div class="asset-info-header">
				<div class="asset-info-main">
				  <div class="asset-info-row">
					<span class="asset-info-label">资产编号</span>
					<span class="asset-info-value">{{ assetInfo.code }}</span>
				  </div>
				  <div class="asset-info-row">
					<span class="asset-info-label">资产名称</span>
					<span class="asset-info-value">{{ assetInfo.name }}</span>
				  </div>
				  <div class="asset-info-row">
					<span class="asset-info-label">资产类别</span>
					<span class="asset-info-value">{{ assetInfo.categoryName }}</span>
				  </div>
				  <div class="asset-info-row">
					<span class="asset-info-label">规格型号</span>
					<span class="asset-info-value">{{ assetInfo.specs }}</span>
				  </div>
				  <div class="asset-info-row">
					<span class="asset-info-label">使用部门</span>
					<span class="asset-info-value">{{ assetInfo.useDeptName }}</span>
				  </div>
				  <div class="asset-info-row">
					<span class="asset-info-label">资产状态</span>
					<span class="asset-info-value">
					  <el-tag
						v-if="assetStatusInfo.label"
						:color="assetStatusInfo.color"
						effect="dark"
						round
					  >
						{{ assetStatusInfo.label }}
					  </el-tag>
					  <span v-else>{{ assetInfo.status }}</span>
					</span>
				  </div>
				</div>
			  </div>
			</div>
					<el-divider></el-divider>

					<!-- 检定计划表单 -->
					<el-form
						v-if="this.type === 'add'"
						:model="form"
						class="form-item"
						:rules="rules"
						ref="form"
						label-width="110px"
					>
						<el-form-item
							label="计划检定日期"
							prop="planAt"
							style="margin-bottom: 15px"
						>
							<el-date-picker
								v-model="form.planAt"
								type="datetime"
								value-format="YYYY-MM-DD HH:mm:ss"
								placeholder="请选择计划检定日期"
							/>
						</el-form-item>
						<el-form-item label="是否循环">
							<el-switch
								v-model="form.recycle"
								active-text="是"
								inactive-text="否"
								:active-value="true"
								:inactive-value="false"
							/>
						</el-form-item>
						<el-form-item
							label="检定部门"
							prop="detectDeptId"
							style="margin-bottom: 15px"
						>
							<wp-input-tree
								v-model="form.detectDeptId"
								:clearable="false"
								:default-text="form.detectDeptName"
								:props="{ children: 'child', label: 'name' }"
								:request="this.$API['uc/org'].getOrgTree.get"
								:show-all="true"
								title="选择检定部门"
							/>
						</el-form-item>
						<el-form-item
							label="检定人员"
							prop="detectBy"
							style="margin-bottom: 15px"
						>
							<wp-select-remote
								v-model="form.detectBy"
								:multiple="false"
								:params="{ status: 'ENABLED', pageSize: 1000 }"
								:placeholder="请选择检定人员"
								:queryKey="keyword"
								:request="this.$API['uc/user'].pageSimple.get"
								style="width: 100%"
							>
							</wp-select-remote>
						</el-form-item>
						<!-- 将检定周期和周期单位放在同一行 -->
						<el-row :gutter="20">
							<el-col :span="12">
								<el-form-item
									label="检定周期"
									prop="period"
									style="margin-bottom: 15px"
									label-width="110px"
								>
									<el-input-number
										v-model="form.period"
										:min="1"
										:max="999"
										style="width: 100%"
									/>
								</el-form-item>
							</el-col>
							<el-col :span="12">
								<el-form-item
									label="周期单位"
									prop="periodUnit"
									style="margin-bottom: 15px"
									label-width="110px"
								>
									<el-select v-model="form.periodUnit" placeholder="请选择周期单位" style="width: 100%"> <!-- 宽度适应父容器 -->
										<el-option label="年" value="YEAR" />
										<el-option label="月" value="MONTH" />
										<el-option label="日" value="DAY" />
									</el-select>
								</el-form-item>
							</el-col>
						</el-row>
						<el-form-item
							class="fullWidth"
							label="备注说明"
							prop="common"
							style="margin-bottom: 15px"
						>
							<el-input
								v-model="form.common"
								maxlength="500"
								placeholder="请输入备注说明"
								show-word-limit
								type="textarea"
							></el-input>
						</el-form-item>
					</el-form>
				</div>
			</template>
			<template #footer>
				<div class="drawer-footer">
					<div class="drawer-button">
						<el-button type="primary" @click="handleSubmit">提交</el-button>
						<el-button @click="closed">取消</el-button>
					</div>
				</div>
			</template>
		</el-drawer>
	</div>
</template>

<script>
// 引入 commonEnum
import commonEnum from "@/api/moudles/dm/common";
// 假设 AssetInfo 模型的路径是正确的，并且包含了 status 字段


export default {
	name: "AppraisalPlanEdit",
	data() {
		return {
			type: "",
			visible: false,
			form: {
				assetId: "",
				planAt: null,
				recycle: false,
				detectBy: "",
				detectDeptId: "",
				period: 1,
				periodUnit: "MONTH", // 将默认值改为 MONTH
				common: "",
				status: "UNCHECK" // 默认状态为待检定
			},
			assetInfo: {
				imageFileRelList: [],
				fileId: null,
                status: '', // 确保 assetInfo 中有 status 字段并初始化
			},
			rules: {
				planAt: [
					{
						type: "date",
						required: true,
						message: "请选择计划检定日期",
						trigger: "change",
					},
				],
				detectDeptId: [
					{
						required: true,
						message: "请选择检定部门",
						trigger: "change",
					},
				],
				detectBy: [
					{
						required: true,
						message: "请选择检定人员",
						trigger: "blur",
					},
				],
				period: [
					{
						required: true,
						message: "请输入检定周期",
						trigger: "blur",
					},
				],
				periodUnit: [
					{
						required: true,
						message: "请选择周期单位",
						trigger: "change", // 或 'blur'
					},
				],
			},
		};
	},
	computed: {
		// 添加一个计算属性来处理资产状态的显示和颜色
		assetStatusInfo() {
			const status = this.assetInfo.status;
			let label = commonEnum.searchDeviceStatus(status); // 使用 commonEnum 获取状态文本
			let color = '';

			// 根据状态设置颜色，这些颜色值应与 detail.vue 中的一致
			switch (status) {
				case 'ENABLED': // 启用
					color = '#48C7A2';
					break;
				case 'DISABLED': // 停用
					color = '#FF636F';
					break;
				case 'SCRAPPED': // 已报废
					color = '#F0AD57';
					break;
				case 'LOSS': // 丢失
					color = '#A0A0A0';
					break;
				case 'TO_SCRAPE': // 待报废
					color = '#D19275';
					break;
				default:
					color = '#F0AD57'; // 其他状态默认颜色
			}
			return { label, color };
		}
	},
	methods: {
		open(param) {
			this.visible = true;
			// 在打开抽屉时重置表单，以清除之前的验证状态和数据
			this.$refs.form?.resetFields();
			// 确保每次打开新增时 periodUnit 有默认值
			if (this.type === 'add' && !this.form.periodUnit) {
                 this.form.periodUnit = 'MONTH';
            }
			return this;
		},

		setData(data) {
			// ObjFilter 可能会过滤掉不需要的字段，这里我们只关心 assetId
			// let params = this.$TOOL.ObjFilter(this.form, data);
			// Object.assign(this.form, params);
            // 将获取的资产详情数据赋值给 assetInfo 和 form 中的 assetId
			this.form.assetId = data.id;
            Object.assign(this.assetInfo, data);

             // 如果是新增操作且periodUnit未被设置，给一个默认值
             if (this.type === 'add' && !this.form.periodUnit) {
                 this.form.periodUnit = 'MONTH';
             }
		},

		async getData(type, id) {
			this.type = type;
			const res = await this.$API["am/assetInfo"].getWithDetail.get(id);
			if (res.success) {
				// assetInfo 在 setData 中被赋值
				this.setData(res.data); // 将获取的完整数据传递给 setData
				this.$nextTick(() => {
					this.$refs.form.validateField('periodUnit');
				});

			} else {
				this.$message.error(res.msg || "操作失败");
			}
		},

		async handleSubmit() {
			this.$refs.form.validate(async (valid) => {
				if (!valid) {
					return;
				}
				let res;
				// 提交时确保周期单位有值，如果用户没有修改，会使用默认值
                if (!this.form.periodUnit) {
                    this.form.periodUnit = 'MONTH';
                }
				res = await this.$API["am/assetAppraisal"].add.post(this.form);

				if (res.success) {
					this.$message.success(res.msg || "操作成功");
					this.visible = false;
					this.$emit("closed");
				} else {
					this.$message.error(res.msg || "操作失败");
				}

				return res;
			});
		},

		closed() {
			this.visible = false;
			this.$emit("closed");
		},
	},
};
</script>

<style lang="scss" scoped>
.content {
	padding: 20px;
}

.out-flex-container {
	margin-bottom: 10px;
}

.inner-flex-container {
	justify-content: space-around;
}
.asset-info-card {
  background: #fafbfc;
  border-radius: 10px;
  box-shadow: 0 2px 8px #0001;
  padding: 18px 20px 10px 20px;
  margin-bottom: 18px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
.asset-info-header {
  display: flex;
  align-items: flex-start;
}
.asset-info-img {
  width: 72px;
  height: 72px;
  border-radius: 8px;
  object-fit: cover;
  margin-right: 18px;
  background: #f0f0f0;
  border: 1px solid #eee;
}
.asset-info-main {
  flex: 1;
  min-width: 0;
}
.asset-info-row {
  display: flex;
  align-items: center;
  min-height: 28px;
  border-bottom: 1px solid #f0f0f0;
  padding: 2px 0;
}
.asset-info-label {
  width: 80px;
  color: #666;
  font-weight: 500;
  text-align: right;
  margin-right: 10px;
  flex-shrink: 0;
  font-size: 14px;
}
.asset-info-value {
  flex: 1;
  color: #222;
  font-size: 14px;
  word-break: break-all;
}
</style>
