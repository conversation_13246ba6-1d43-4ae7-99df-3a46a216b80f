import {h, resolveComponent} from "vue";

// 搜索model
export const listQuery = {
	name: "",
	code: "",
	barcode: "",
	entryStatus: "CONFIRMED",
	failurePage: "true",
	status: "ENABLED",
};

// 搜索表单配置
export const searchConfig = function () {
	return {
		list: [
			{
				type: "input",
				label: "资产编号",
				model: "code",
				options: {
					placeholder: "请输入资产编号",
				},
			},
			{
				type: "input",
				label: "资产名称",
				model: "name",
				options: {
					placeholder: "请输入资产名称",
				},
			},
			{
				type: "inputTree",
				label: "资产类别",
				model: "categoryId",
				options: {
					request: this.$API["am/category"].getAssetCategoryTree.get,
					items: [],
					props: {
						children: "children",
						label: "name"
					},
					params: {type: "AC"},
					includeSelf: false
				},
			},
		],
		config: {
			size: "default",
			onSearch: () => {
				this.handleSubmit();
			},
			model: this.listQuery,
		},
	};
};

// 表格展示配置
export const tableColumns = function () {
	return [
		{
			prop: "code",
			label: "资产编号",
			width: 120,
		},
		{
			prop: "name",
			label: "资产名称",
			width: 150,
		},
		{
			prop: "categoryName",
			label: "资产类别",
			width: 120,
		},
		{
			prop: "specs",
			label: "规格型号",
			width: 120,
		},
		{
			prop: "quantity",
			label: "数量",
			width: 80,
		},
		{
			prop: "unitPrice",
			label: "单价",
			width: 100,
		},
		{
			prop: "totalPrice",
			label: "总价",
			width: 100,
		},
		{
			prop: "supplierName",
			label: "供应商",
			width: 120,
		},
		{
			prop: "purchaseAt",
			label: "购买日期",
			width: 100,
		},
		{
			prop: "belongDeptName",
			label: "所属部门",
			width: 120,
		},
		{
			prop: "useDeptName",
			label: "使用部门",
			width: 120,
		},
		{
			prop: "status",
			label: "资产状态",
			width: 80,
			render: (row) => {
				const statusMap = {
					ENABLED: {label: "启用", color: "#48C7A2"},
					DISABLED: {label: "禁用", color: "#FF636F"},
					SCRAPED: {label: "报废", color: "#F0AD57"},
					LOSS: {label: "丢失", color: "#A0A0A0"},
					TO_SCRAPE: {label: "待报废", color: "#D19275"},
				};
				const status = statusMap[row.status] || {label: row.status || "未知", color: "#A0A0A0"};
				return h(
					resolveComponent("el-tag"),
					{color: status.color, effect: "dark", round: true},
					status.label
				);
			},
		},
		{
			prop: "opt",
			fixed: "right",
			label: "操作",
			align: "center",
			width: 110,
			render: (row) => {
				const optArr = [];
				const editBtn = h(resolveComponent("wp-tip-button"), {
					content: "选择",
					type: "text",
					icon: "wp-icon-apply",
					onClick: () => {
						this.handleOpt("add", row);
					},
				});

				optArr.push(editBtn);
				return h("div", null, optArr);
			},
		},
	];
};
