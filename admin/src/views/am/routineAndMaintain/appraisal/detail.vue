<template>
	<div class="drawer detail">
		<el-drawer
			v-model="visible"
			:size="640"
			custom-class="detail-drawer"
			destroy-on-close
			:before-close="refresh"
			@closed="closed"
		>
			<template #header>
				<div class="title">
					<div class="line"></div>
					<span>{{ titleDrawer }}</span>
				</div>
			</template>
			<template #default>
				<div class="drawer-content detail">
					<el-form ref="form" class="form-item">
						<div class="section-title">
							<div class="section-title-bar"></div>
							<span>基本信息</span>
						</div>
						<div class="asset-info-card" v-if="assetInfo && assetInfo.id">
							<wp-image
								:file="assetInfo.imageFileRelList[0].fileId"
								:errorImage="require(`@/assets/images/common/img_instrument_default.svg`)"
								class="asset-info-img"
							></wp-image>
							<div class="asset-info-main">
								<div class="asset-info-row">
									<span class="asset-info-label1">资产编号</span>
									<span class="asset-info-value">{{ assetInfo.code }}</span>
								</div>
								<div class="asset-info-row">
									<span class="asset-info-label1">资产名称</span>
									<span class="asset-info-value">{{ assetInfo.name }}</span>
								</div>
								<div class="asset-info-row">
									<span class="asset-info-label1">资产类别</span>
									<span class="asset-info-value">{{ assetInfo.categoryName }}</span>
								</div>
								<div class="asset-info-row">
									<span class="asset-info-label1">规格型号</span>
									<span class="asset-info-value">{{ assetInfo.specs }}</span>
								</div>
								<div class="asset-info-row">
									<span class="asset-info-label1">数量</span>
									<span class="asset-info-value">{{ assetInfo.quantity }}</span>
								</div>
								<div class="asset-info-row">
									<span class="asset-info-label1">单价</span>
									<span class="asset-info-value">{{ assetInfo.unitPrice }}</span>
								</div>
								<div class="asset-info-row">
									<span class="asset-info-label1">总价</span>
									<span class="asset-info-value">{{ assetInfo.totalPrice }}</span>
								</div>
								<div class="asset-info-row">
									<span class="asset-info-label1">供应商</span>
									<span class="asset-info-value">{{ assetInfo.supplierName }}</span>
								</div>
								<div class="asset-info-row">
									<span class="asset-info-label1">购买日期</span>
									<span class="asset-info-value">{{ assetInfo.purchaseAt }}</span>
								</div>
								<div class="asset-info-row">
									<span class="asset-info-label1">所属部门</span>
									<span class="asset-info-value">{{ assetInfo.belongDeptName }}</span>
								</div>
								<div class="asset-info-row">
									<span class="asset-info-label1">使用部门</span>
									<span class="asset-info-value">{{ assetInfo.useDeptName }}</span>
								</div>
								<div class="asset-info-row">
									<span class="asset-info-label1">资产状态</span>
									<span class="asset-info-value">{{ assetInfo.status }}</span>
								</div>
								<div class="asset-info-row">
									<span class="asset-info-label1">出厂日期</span>
									<span class="asset-info-value">{{ assetInfo.produceAt }}</span>
								</div>
								<div class="asset-info-row">
									<span class="asset-info-label1">出厂编号</span>
									<span class="asset-info-value">{{ assetInfo.produceCode }}</span>
								</div>
								<div class="asset-info-row">
									<span class="asset-info-label1">制造商</span>
									<span class="asset-info-value">{{ assetInfo.produceName }}</span>
								</div>
								<div class="asset-info-row">
									<span class="asset-info-label1">国别</span>
									<span class="asset-info-value">{{ assetInfo.countriesArea }}</span>
								</div>
							</div>
						</div>
					</el-form>
					<el-divider></el-divider>
					<div class="section-title">
						<div class="section-title-bar"></div>
						<span>检定信息</span>
					</div>

					<div class="form-card">
						<div class="asset-info-main">
							<div class="asset-info-row">
								<span class="asset-info-label">检定编号</span>
								<span class="asset-info-value">{{ form.code }}</span>
							</div>
							<div class="asset-info-row">
								<span class="asset-info-label">检定状态</span>
								<span class="asset-info-value">{{ searchStatus(form.status) }}</span>
							</div>
							<div class="asset-info-row">
								<span class="asset-info-label">计划检定日期</span>
								<span class="asset-info-value">{{ form.planAt }}</span>
							</div>
							<div class="asset-info-row">
								<span class="asset-info-label">是否循环</span>
								<span class="asset-info-value">
									<el-tag
										:type="form.recycle ? 'success' : 'info'"
										effect="dark"
										round
									>
										{{ form.recycle ? '是' : '否' }}
									</el-tag>
								</span>
							</div>
							<div class="asset-info-row">
								<span class="asset-info-label">检定部门</span>
								<span class="asset-info-value">{{ form.detectDeptName }}</span>
							</div>
							<div class="asset-info-row">
								<span class="asset-info-label">检定人员</span>
								<span class="asset-info-value">{{ form.detectByName }}</span>
							</div>
							<div class="asset-info-row">
								<span class="asset-info-label">检定周期</span>
								<span class="asset-info-value">{{ form.period }} {{ form.periodUnit === 'YEAR' ? '年' : (form.periodUnit === 'MONTH' ? '月' : (form.periodUnit === 'DAY' ? '天' : form.periodUnit)) }}</span>
							</div>
							<div class="asset-info-row">
								<span class="asset-info-label">检定结果</span>
								<span class="asset-info-value">{{ searchResult(form.result) }}</span>
							</div>
							<div class="asset-info-row">
								<span class="asset-info-label">检定备注</span>
								<span class="asset-info-value">{{ form.common }}</span>
							</div>
						</div>
					</div>
					<div class="asset-info-row" v-if="form.examineImageFileList && form.examineImageFileList.length > 0">
						<span class="asset-info-label">作业图片</span>
						<span class="asset-info-value">
							<ul class="el-upload-list image">
							  <li
								  v-for="item in form.examineImageFileList"
								  :key="item.fileId"
								  class="el-upload-list__item"
							  >
								<el-image
									:preview-src-list="[solutionSrc(item.fileId)]"
									:src="solutionSrc(item.fileId)"
									alt="点击查看大图"
									preview-teleported
									style="width: 120px; height: 120px"
									title="点击查看大图"
								>
								  <template #error>
									<el-image
										:src="$imageDefault"
										style="width: 120px; height: 120px"
									></el-image>
								  </template>
								</el-image>
							  </li>
							</ul>
					  </span>
					</div>
					<div class="asset-info-row" v-if="form.examineAttrFileList && form.examineAttrFileList.length > 0">
						<span class="asset-info-label">相关附件</span>
						<span class="asset-info-value">
							<ul class="el-upload-list">
							  <li
								  v-for="item in form.examineAttrFileList"
								  :key="item.fileId"
								  class="el-upload-list__item"
							  >
								<div class="el-upload-list__item-info">
								  <a
									  :href="solutionSrc(item.fileId)"
									  class="el-upload-list__item-name"
									  target="_blank"
								  >
									<el-icon>
									  <el-icon-document />
									</el-icon>
									<span class="el-upload-list__item-file-name">
									  {{ item.fileName }}
									</span>
								  </a>
								</div>
							  </li>
							</ul>
					    </span>
					</div>
				</div>
			</template>
			<template #footer>
				<div class="drawer-footer">
					<!-- 详情页面通常没有操作按钮，或只有关闭按钮 -->
					<div class="drawer-button">
						<el-button @click="closed">关闭</el-button>
					</div>
				</div>
			</template>
		</el-drawer>
	</div>
</template>

<script>
import commonEnum from "@/api/moudles/am/common"; // 假设commonEnum包含状态和结果的映射
import uploadConfig from "@/config/upload"; // 假设有上传配置用于预览文件

export default {
	name: "assetAppraisalDetail", // 修改组件名
	props: {
		widthDrawer: {type: Number, default: 640},
		titleDrawer: {type: String, default: "资产检定详情"}, // 修改标题
	},
	data() {
		return {
			visible: false,
			// form数据对应单条检定计划记录
			form: {
				id: "",
				assetId: "", // 关联的资产ID
				code: "", // 检定编号
				planAt: "", // 计划检定日期
				detectAt: "", // 实际检定日期
				recycle: "", // 是否循环
				detectBy: "", // 检定人ID
				detectByName: "", // 检定人姓名
				detectDeptId: "", // 检定部门ID
				detectDeptName: "", // 检定部门名称
				period: null, // 检定周期
				periodUnit: "", // 检定周期单位
				status: "", // 检定状态
				result: "", // 检定结果
				common: "", // 检定备注内容
				// TODO: 如果检定作业有图片和附件，需要在此处添加对应的list字段
				examineImageFileList: [], // 检定作业图片列表
				examineAttrFileList: [], // 检定作业附件列表
			},
			// 关联资产的基本信息
			assetInfo: {
				// 包含资产基础字段，与 asset/detail.vue 中的 assetInfo 结构类似
				id: "",
				code: "",
				name: "",
				categoryName: "",
				specs: "",
				quantity: null,
				unitPrice: null,
				totalPrice: null,
				supplierName: "",
				purchaseAt: "",
				belongDeptName: "",
				useDeptName: "",
				status: "",
				produceAt: "",
				produceCode: "",
				produceName: "",
				countriesArea: "",
				fileId: null, // 用于展示图片
				imageFileRelList: [],
			},
			drawerTitle: require("@/assets/images/drawer/drawer_icon_title.png"), // 保持图片路径
		};
	},
	methods: {
		open() {
			this.visible = true;
			return this;
		},

		// 设置详情数据，data是接口返回的单条检定计划记录
		async setData(data) {
			// 将返回的检定计划数据赋值给 form
			Object.assign(this.form, data);

			// 根据检定计划中的 assetId 获取关联资产的详细信息
			if (data.assetId) {
				const assetRes = await this.$API["am/assetInfo"].getWithDetail.get(data.assetId); // TODO: 确认获取资产详情的API
				if (assetRes.success) {
					Object.assign(this.assetInfo, assetRes.data);
					// 处理资产图片的 fileId
					if (this.assetInfo.imageFileRelList && this.assetInfo.imageFileRelList.length > 0) {
						this.assetInfo.fileId = this.assetInfo.imageFileRelList[0].fileId;
					}
					// 处理国别显示
					if (this.assetInfo.countriesArea) {
						this.assetInfo.countriesArea = commonEnum.searchKeys(
							commonEnum.countriesArea,
							this.assetInfo.countriesArea
						);
					}
				} else {
					this.$message.error(assetRes.msg || "获取关联资产信息失败");
				}
			} else {
				this.assetInfo = {}; // 如果没有 assetId，清空资产信息
			}

			// TODO: 如果检定作业的图片和附件是存储在检定记录中的，直接赋值即可
			// 例如：this.form.examineImageFileList = data.examineImageFileList;
			// 例如：this.form.examineAttrFileList = data.examineAttrFileList;

		},

		// 预览文件
		solutionSrc(fileId) {
			return uploadConfig.solutionSrc(fileId);
		},

		closed() {
			// 重置 form 和 assetInfo 数据，避免下次打开显示旧数据
			this.form = {
				id: "",
				assetId: "",
				code: "",
				planAt: "",
				detectAt: "",
				recycle: "",
				detectBy: "",
				detectByName: "",
				detectDeptId: "",
				detectDeptName: "",
				period: null,
				periodUnit: "",
				status: "",
				result: "",
				common: "",
				examineImageFileList: [],
				examineAttrFileList: []
			};
			this.assetInfo = {
				id: "",
				code: "",
				name: "",
				categoryName: "",
				specs: "",
				quantity: null,
				unitPrice: null,
				totalPrice: null,
				supplierName: "",
				purchaseAt: "",
				belongDeptName: "",
				useDeptName: "",
				status: "",
				produceAt: "",
				produceCode: "",
				produceName: "",
				countriesArea: "",
				fileId: null,
				imageFileRelList: []
			};
			this.visible = false;
			this.$emit("closed"); // 通知父组件已关闭
		},

		refresh() {
			// before-close 时的处理，通常只是关闭 modal
			this.visible = false;
			this.$emit("closed");
		},
		// TODO: 实现状态和结果的转换逻辑，与 index.vue 中的方法保持一致
		searchStatus(status) {
			const statusMap = {
				UNCHECK: "待检定",
				CHECKED: "已检定",
			};
			return statusMap[status] || status;
		},
		searchResult(result) {
			const resultMap = {
				QUALIFIED: "合格",
				UNQUALIFIED: "不合格",
			};
			return resultMap[result] || result;
		}
	},
};
</script>

<style lang="scss" scoped>
:deep(.el-tabs__content) {
	overflow: visible;
}

// 保留原有样式
.el-upload-list {
	margin-top: 0;
}

.el-icon {
	color: #009fa8 !important;
}

.el-upload-list__item-file-name {
	color: #009fa8 !important;
}

.section-title {
	display: flex;
	align-items: center;
	font-size: 16px;
	font-weight: bold;
	margin: 18px 0 12px 0;
	padding-left: 0;
}

.section-title-bar {
	width: 4px;
	height: 20px;
	background: #409eff;
	border-radius: 2px;
	margin-right: 8px;
}

.asset-info-card {
	background: #fafbfc;
	border-radius: 10px;
	box-shadow: 0 2px 8px #0001;
	padding: 18px 20px 10px 20px;
	margin-bottom: 18px;
	display: flex;
	flex-direction: row;
	align-items: flex-start;
}

.asset-info-img {
	width: 72px;
	height: 72px;
	border-radius: 8px;
	object-fit: cover;
	margin-right: 18px;
	background: #f0f0f0;
	border: 1px solid #eee;
}

.asset-info-main {
	flex: 1;
	min-width: 0;
}

.asset-info-row {
	display: flex;
	align-items: center;
	min-height: 28px;
	border-bottom: 1px solid #f0f0f0;
	padding: 2px 0;
}

.asset-info-label {
	width: 110px;
	color: #666;
	font-weight: 500;
	text-align: right;
	margin-right: 10px;
	flex-shrink: 0;
	font-size: 14px;
}

.asset-info-label1 {
	width: 80px;
	color: #666;
	font-weight: 500;
	text-align: right;
	margin-right: 10px;
	flex-shrink: 0;
	font-size: 14px;
}

.asset-info-value {
	flex: 1;
	color: #222;
	font-size: 14px;
	word-break: break-all;
}

.form-card {
	background: #fafbfc;
	border-radius: 10px;
	box-shadow: 0 2px 8px #0001;
	padding: 18px 20px 10px 20px;
	margin-bottom: 18px;
	margin-top: 0;
}
</style>
