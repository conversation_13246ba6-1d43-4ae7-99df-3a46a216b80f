<template>
	<div class="drawer detail">
		<el-drawer
			v-model="visible"
			:size="640"
			custom-class="detail-drawer"
			destroy-on-close
			@closed="$emit('closed')"
		>
			<template #header>
				<div class="title">
					<div class="line"></div>
					<span>{{ title }}</span>
				</div>
			</template>
			<template #default>
				<div class="content">
					<el-form
						ref="form"
						:model="form"
						class="form-item"
						label-position="left"
						label-width="100px"
					>
						<div class="drawer-item">
							<div class="drawer-item-title">
								<img :src="drawerTitle" alt="基础信息"/>
								<span>基础信息</span>
							</div>
						</div>
						<el-row :gutter="20" class="mt10">
							<el-col :span="12">
								<el-form-item label="资产编号">
									{{ form.code || '系统自动生成' }}
								</el-form-item>
							</el-col>
							<el-col :span="12">
								<el-form-item label="资产名称">
									{{ form.name }}
								</el-form-item>
							</el-col>
							<el-col :span="12">
								<el-form-item label="资产类别">
									{{ form.categoryName }}
								</el-form-item>
							</el-col>
							<el-col :span="12">
								<el-form-item label="规格型号">
									{{ form.specs }}
								</el-form-item>
							</el-col>
							<el-col :span="12">
								<el-form-item label="数量">
									{{ form.quantity }}
								</el-form-item>
							</el-col>
							<el-col :span="12">
								<el-form-item label="单价">
									{{ form.unitPrice }} 元
								</el-form-item>
							</el-col>
							<el-col :span="12">
								<el-form-item label="总价">
									{{ form.totalPrice }} 元
								</el-form-item>
							</el-col>
							<el-col :span="12">
								<el-form-item label="状态" style="height: 32px">
									<!-- 修改资产状态的显示逻辑 -->
									<el-tag
										v-if="assetStatusInfo.label"
										:color="assetStatusInfo.color"
										effect="dark"
										round
									>
										{{ assetStatusInfo.label }}
									</el-tag>
									<span v-else>{{ form.status }}</span> <!-- 如果没有对应的标签，显示原始状态 -->
								</el-form-item>
							</el-col>
							<el-col :span="12">
								<el-form-item
									label="入库状态"
									style="height: 32px"
								>
									<!-- 入库状态的显示逻辑保持不变 -->
									<el-tag
										v-if="form.entryStatus === 'APPROVED'"
										color="#48C7A2"
										effect="dark"
										round
									>
										已入库
									</el-tag>
									<el-tag
										v-else-if="form.entryStatus === 'CANCELED'"
										color="#A0A0A0"
										effect="dark"
										round
									>
										已取消
									</el-tag>
									<el-tag
										v-else-if="form.entryStatus === 'APPROVE'"
										color="#F0AD57"
										effect="dark"
										round
									>
										待入库
									</el-tag>
									<el-tag
										v-else
										color="#F0AD57"
										effect="dark"
										round
									>
										{{ form.entryStatus }}
									</el-tag>
								</el-form-item>
							</el-col>
							<el-col :span="24">
								<el-form-item label="制造商">
									{{ form.produceName }}
								</el-form-item>
							</el-col>
							<el-col :span="24">
								<el-form-item label="供应商">
									{{ form.supplierName }}
								</el-form-item>
							</el-col>
							<el-col :span="12">
								<el-form-item label="出厂编号">
									{{ form.produceCode }}
								</el-form-item>
							</el-col>
							<el-col :span="12">
								<el-form-item label="申请人">
									{{ form.applyByName }}
								</el-form-item>
							</el-col>
						</el-row>
						<div class="drawer-item">
							<div class="drawer-item-title">
								<img :src="drawerTitle" alt="使用信息"/>
								<span>使用信息</span>
							</div>
						</div>
						<el-row :gutter="20" class="mt10">
							<el-col :span="12">
								<el-form-item label="购买日期">
									{{ subDate(form.purchaseAt) }}
								</el-form-item>
							</el-col>
							<el-col :span="12">
								<el-form-item label="所属部门">
									{{ form.belongDeptName }}
								</el-form-item>
							</el-col>
							<el-col :span="12">
								<el-form-item label="使用部门">
									{{ form.useDeptName }}
								</el-form-item>
							</el-col>
							<el-col :span="12">
								<el-form-item label="管理员">
									{{ form.adminUserName }}
								</el-form-item>
							</el-col>
							<el-col :span="12">
								<el-form-item label="使用人">
									{{ form.useUserName }}
								</el-form-item>
							</el-col>
						</el-row>
						<div class="drawer-item">
							<div class="drawer-item-title">
								<img :src="drawerTitle" alt="其他信息"/>
								<span>其他信息</span>
							</div>
						</div>
						<el-row :gutter="20" class="mt10">
							<el-col :span="24">
								<el-form-item
									label="资产图片"
									style="height: auto"
								>
									<ul class="el-upload-list image"
										v-if="form.imageFileRelList && form.imageFileRelList.length > 0">
										<li
											v-for="item in form.imageFileRelList"
											:key="item.fileId"
											class="el-upload-list__item"
										>
											<el-image
												:preview-src-list="[
													solutionSrc(item.fileId),
												]"
												:src="solutionSrc(item.fileId)"
												alt="点击查看大图"
												preview-teleported
												style="
													width: 120px;
													height: 120px;
												"
												title="点击查看大图"
											>
												<template #error>
													<el-image
														:src="$imageDefault"
														style="
															width: 120px;
															height: 120px;
														"
													></el-image>
												</template>
											</el-image>
										</li>
									</ul>
									<div v-else class="no-image">暂无图片</div>
								</el-form-item>
							</el-col>
							<el-col :span="24">
								<el-form-item label="备注">
									{{ form.common }}
								</el-form-item>
							</el-col>
						</el-row>
						<div class="drawer-item" v-if="approveFlag">
							<div class="drawer-item-title">
								<img :src="drawerTitle" alt="审核信息"/>
								<span>审核信息</span>
							</div>
						</div>
						<el-row :gutter="20" class="mt10" v-if="approveFlag">
							<el-col :span="12">
								<el-form-item label="审核人员">
									{{ form.approveByName }}
								</el-form-item>
							</el-col>
							<el-col :span="12">
								<el-form-item label="审核日期">
									{{ form.approveAt }}
								</el-form-item>
							</el-col>
							<el-col :span="24">
								<el-form-item label="审核备注">
									{{ form.approveComment }}
								</el-form-item>
							</el-col>
						</el-row>
					</el-form>
				</div>
			</template>
			<template #footer>
				<div class="drawer-footer">
					<div class="drawer-button">
						<el-button @click="closed">关闭</el-button>
					</div>
				</div>
			</template>
		</el-drawer>
	</div>
</template>

<script>
import tool from "@/utils/tool";
import uploadConfig from "@/config/upload";
// 引入 commonEnum
import commonEnum from "@/api/moudles/am/common";


export default {
	name: "assetInfoDetail",
	data() {
		return {
			visible: false,
			title: null,
			form: {},
			drawerTitle: require("@/assets/images/drawer/drawer_icon_title.png"),
			approveFlag: false,
		};
	},
	computed: {
		// 添加一个计算属性来处理资产状态的显示和颜色
		assetStatusInfo() {
			const status = this.form.status;
			let label = commonEnum.searchDeviceStatus(status); // 使用 commonEnum 获取状态文本
			let color = '';

			// 根据状态设置颜色，颜色值需要根据实际情况确定
			switch (status) {
				case 'ENABLED': // 启用
					color = '#48C7A2';
					break;
				case 'DISABLED': // 停用
					color = '#FF636F';
					break;
				case 'SCRAPPED': // 已报废
					color = '#F0AD57';
					break;
				case 'LOSS': // 丢失
					color = '#A0A0A0';
					break;
				case 'TO_SCRAPE': // 待报废
					color = '#D19275';
					break;
				default:
					color = '#F0AD57'; // 其他状态默认颜色
			}
			return {label, color};
		}
	},
	methods: {
		open(param) {
			this.visible = true;
			if (param && param.title) {
				this.title = param.title;
			}
			return this;
		},
		setData(data) {
			Object.assign(this.form, data);
			// 使用entryStatus替换approveStatus
			this.approveFlag = this.form.entryStatus !== "APPROVE";
		},
		closed() {
			this.visible = false;
			this.$emit("closed");
		},
		subDate(date) {
			return date ? tool.subDate(date) : "";
		},
		solutionSrc(fileId) {
			return uploadConfig.solutionSrc(fileId);
		}
	},
};
</script>

<style lang="scss" scoped>
.content {
	padding: 20px;
}

.out-flex-container {
	margin-bottom: 10px;
}

.inner-flex-container {
	justify-content: space-around;
}

.el-upload-list__item {
	transition: none !important;
}
</style>
