<template>
	<el-dialog
		v-model="dialogVisible"
		title="入库审核"
		:visible="dialogVisible"
		width="30%"
	>
		<div>
			<el-form
				:label-position="labelPosition"
				label-width="100px"
				:model="form"
			>
				<el-form-item label="审核备注">
					<el-input
						v-model.trim="form.approveComment"
						:autosize="{ minRows: 4, maxRows: 6 }"
						maxlength="500"
						show-word-limit
						type="textarea"
						placeholder="请输入审核备注"
					/>
				</el-form-item>
			</el-form>
		</div>
		<template #footer>
			<div class="dialog-footer">
				<el-button @click="handleDetail">查看详情</el-button>
				<el-button type="primary" @click="handleOpt('APPROVED')">审核通过</el-button>
				<el-button @click="handleOpt('REJECTED')">审核拒绝</el-button>
				<el-button @click="closed">取消</el-button>
			</div>
		</template>
	</el-dialog>
</template>
<script>
export default {
	name: "auditDialog",
	data() {
		return {
			dialogVisible: false,
			labelPosition: "top", // 表单域标签的位置
			row: {},
			form: {
				id: "",
				approveComment: "",
			},
		};
	},
	methods: {
		closed() {
			this.dialogVisible = false;
			this.$emit("closed");
		},
		open(row) {
			this.row = row;
			this.dialogVisible = true;
			this.form.id = row.id;
			this.form.approveComment = "";
			return this;
		},
		async handleOpt(type) {
			let result;
			let param = {};
			param.id = this.row.id;
			param.entryStatus = type;
			param.approveComment = this.form.approveComment;
			
			switch (type) {
				case "APPROVED":
					result = await this.$API["am/assetInfo"].approve.post(param);
					break;
				case "REJECTED":
					result = await this.$API["am/assetInfo"].reject.post(param);
					break;
				default:
			}

			if (result && result.success) {
				this.closed();
				this.$message.success(result.msg || "审核操作成功");
				this.$emit("success");
			} else if (result) {
				this.$message.error(result.msg || "操作失败");
			}
		},
		handleDetail() {
			this.closed();
			this.$emit("openDetail", this.row);
		},
	},
};
</script>

<style lang="scss" scoped>
.dialog-footer {
	display: flex;
	justify-content: flex-end;
	align-items: center;
	
	.el-button {
		margin-left: 10px;
	}
}
</style> 