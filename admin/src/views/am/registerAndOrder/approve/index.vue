<template>
	<el-container>
		<el-aside width="auto">
			<el-container>
				<el-header style="padding-top: 10px">
					<el-input
						v-model.trim="filterText"
						clearable
						placeholder="输入关键字过滤"
					></el-input>
				</el-header>
				<el-main class="nopadding">
					<el-tree
						ref="categoryTreeRef"
						:data="categoryTreeData"
						:props="defaultProps"
						:filter-node-method="filterNode"
						node-key="id"
						:expand-on-click-node="false"
						default-expand-all
						:highlight-current="true"
						class="menu"
						@node-click="handleNodeClick"
					>
						<template #default="{ node, data }">
							<span class="custom-tree-node">
								<span>{{ node.label }}</span>
							</span>
						</template>
					</el-tree>
				</el-main>
			</el-container>
		</el-aside>

		<!-- Right Side: Main Content -->
		<el-container>
			<el-header>
				<wp-search-form :data="searchConfig"></wp-search-form>
			</el-header>
			<!-- Removed original search header -->
			<el-main class="nopadding">
				<wpTable
					ref="table"
					:apiObj="apiObj"
					:column="columns"
					:params="listQuery"
					row-key="id"
					stripe
					@selection-change="handleSelectionChange"
				>
					<template #status="scope">
						<el-tag
							v-if="scope.row.status === 'ENABLED'"
							color="#48C7A2"
							effect="dark"
							round
						>
							{{ searchStatus(scope.row.status) }}
						</el-tag>
						<el-tag
							v-if="scope.row.status === 'DISABLED'"
							color="#FF636F"
							effect="dark"
							round
						>
							{{ searchStatus(scope.row.status) }}
						</el-tag>
						<el-tag
							v-if="scope.row.status === 'SCRAPED'"
							color="#F0AD57"
							effect="dark"
							round
						>
							{{ searchStatus(scope.row.status) }}
						</el-tag>
						<el-tag
							v-if="scope.row.status === 'LOSS'"
							color="#A0A0A0"
							effect="dark"
							round
						>
							{{ searchStatus(scope.row.status) }}
						</el-tag>
						<el-tag
							v-if="scope.row.status === 'TO_SCRAPE'"
							color="#D19275"
							effect="dark"
							round
						>
							{{ searchStatus(scope.row.status) }}
						</el-tag>
					</template>
					<template #entryStatus="scope">
						<el-tag
							v-if="scope.row.entryStatus === 'APPROVE'"
							color="#59A1FF"
							effect="dark"
							round
						>
							待审核
						</el-tag>
						<el-tag
							v-if="scope.row.entryStatus === 'APPROVED'"
							color="#48C7A2"
							effect="dark"
							round
						>
							已审核
						</el-tag>
						<el-tag
							v-if="scope.row.entryStatus === 'REJECTED'"
							color="#FF636F"
							effect="dark"
							round
						>
							已拒绝
						</el-tag>
						<el-tag
							v-if="scope.row.entryStatus === 'CANCELED'"
							color="#A0A0A0"
							effect="dark"
							round
						>
							已取消
						</el-tag>
						<el-tag
							v-if="scope.row.entryStatus === 'CONFIRMED'"
							color="#48C7A2"
							effect="dark"
							round
						>
							已确认
						</el-tag>
					</template>
				</wpTable>
			</el-main>
		</el-container>

		<!-- Modals remain the same -->
		<detail-modal
			v-if="showDetail"
			ref="DetailModal"
			@closed="showDetail = !showDetail"
			@success="handleSubmit"
		></detail-modal>
		<audit-dialog
			v-if="showAuditDialog"
			ref="AuditDialog"
			@closed="showAuditDialog = !showAuditDialog"
			@openDetail="openDetail"
			@success="handleSubmit"
		></audit-dialog>
	</el-container>
</template>

<script>
import {listQuery, searchConfig, tableColumns} from "./config";
import DetailModal from "./detail";
import commonEnum from "@/api/moudles/am/common";
import AuditDialog from "./auditDialog.vue";

export default {
	components: {
		AuditDialog,
		DetailModal,
	},
	name: "approveIndex",
	data() {
		return {
			apiObj: this.$API["am/assetInfo"].page,
			columns: tableColumns.bind(this)(),
			searchConfig: null,
			listQuery: Object.assign({}, listQuery),
			selections: [],
			visible: false,
			showDetail: false,
			dialogVisible: false,
			showDialog: false,
			showAuditDialog: false,
			categoryTreeData: [],
			filterText: "",
			defaultProps: {
				children: "children",
				label: "name",
			},
		};
	},
	watch: {
		filterText(val) {
			this.$refs.categoryTreeRef.filter(val);
		},
	},
	methods: {
		async getCategoryTree() {
			try {
				const res = await this.$API["am/category"].getEnabledCategoryTree.get("AC");
				if (res.success) {
					res.data.id = null;
					this.categoryTreeData = [res.data];
				} else {
					this.$message.error("获取分类失败: " + res.msg);
					this.categoryTreeData = [{id: null, name: "全部分类"}];
				}
			} catch (error) {
				console.error("Error fetching category tree:", error);
				this.$message.error("获取分类时出错");
				this.categoryTreeData = [{id: null, name: "全部分类"}];
			}
		},
		filterNode(value, data) {
			if (!value) return true;
			return data.name.includes(value);
		},
		handleNodeClick(data) {
			this.listQuery.categoryId = data.id;
			this.handleSubmit();
		},
		handleSubmit() {
			this.$refs.table.upData(this.listQuery);
		},
		openDetail(row) {
			this.handleOpt("detail", row);
		},
		handleSelectionChange(val) {
			this.selections = val;
		},
		async handleOpt(type, row) {
			console.log("row===>", row);
			let result = null;
			row.dialogType = type;
			switch (type) {
				case "detail":
					this.showDetail = true;
					result = await this.$API["am/assetInfo"].getWithDetail.get(
						row.id
					);
					this.$nextTick(() => {
						if (result.success) {
							this.$refs.DetailModal.open({
								title: "资产详情",
							}).setData(result.data);
						}
					});
					break;
				case "check":
					this.showAuditDialog = true;
					this.$nextTick(() => {
						this.$refs.AuditDialog.open(row);
					});
					break;
				default:
			}
		},
		searchStatus(status) {
			return commonEnum.searchDeviceStatus(status);
		},
	},

	computed: {},
	created() {
		this.searchConfig = searchConfig.bind(this)();
	},
	mounted() {
		this.getCategoryTree();
	},
};
</script>
