import { h, resolveComponent } from "vue";
import commonEnum from "@/api/moudles/am/common";
import uploadConfig from "@/config/upload";
const imageDefault = require("@/assets/images/common/img_instrument_default.svg");

// 搜索model
export const listQuery = {
	name: "",
	code: "",
	barCode: "",
	buildId: "",
	specs: "",
	produceName: "",
	supplierName: "",
	countriesArea: "",
	applyType: "",
	controlMode: "",
	belongOrgId: "",
	entryStatus: "APPROVE"
};

// 搜索表单配置
export const searchConfig = function () {
	return {
		list: [
			{
				type: "datetimerange",
				label: "审核时间",
				model: "approveAt",
				options: {},
			},
			{
				type: "input",
				label: "名称搜索",
				model: "name",
				options: {
					placeholder: "请输入资产名称",
				},
			},
			{
				type: "input",
				label: "规格型号",
				model: "specs",
				options: {},
			},

		],
		config: {
			size: "default",
			onSearch: () => {
				this.handleSubmit();
			},
			model: this.listQuery,
		},
	};
};
export const solutionImageSrc = function (fileId) {
	return uploadConfig.solutionImageSrc(fileId);
};
// 表格展示配置
export const tableColumns = function () {
	return [
		{
			type: "checkbox",
			fixed: "center",
		},
		{
			prop: "fileId",
			label: "图片",
			width: 120,
			render: (row) => {
				let fileIds = row.imageFileRelList;
				let url = [];
				for (let index in fileIds) {
					let httpUrl = solutionImageSrc(fileIds[index]["fileId"]);
					url.push(httpUrl);
				}
				if (url.length <= 0) {
					return h(
						resolveComponent("el-image"),
						{
							src: imageDefault,
							style: {
								height: "40px",
								width: "40px",
							},
						},
						""
					);
				} else {
					return h(
						resolveComponent("el-image"),
						{
							src: url[0],
							"preview-src-list": url,
							"preview-teleported": true,
							style: {
								height: "40px",
								width: "40px",
							},
						},
						""
					);
				}
			},
		},
		{
			prop: "code",
			label: "资产编号",
			width: 120,
		},
		{
			prop: "name",
			label: "资产名称",
			width: 150,
		},
		{
			prop: "categoryName",
			label: "资产类别",
			width: 120,
		},
		{
			prop: "specs",
			label: "规格型号",
			width: 120,
		},
		{
			prop: "quantity",
			label: "数量",
			width: 80,
		},
		{
			prop: "unitPrice",
			label: "单价",
			width: 100,
		},
		{
			prop: "totalPrice",
			label: "总价",
			width: 100,
		},
		{
			prop: "supplierName",
			label: "供应商",
			width: 120,
		},
		{
			prop: "purchaseAt",
			label: "购买日期",
			width: 100,
		},
		{
			prop: "belongDeptName",
			label: "所属部门",
			width: 120,
		},
		{
			prop: "useDeptName",
			label: "使用部门",
			width: 120,
		},
		{
			prop: "applyByName",
			label: "申请人",
			width: 100,
		},
		{
			prop: "approveByName",
			label: "审核人",
			width: 100,
		},
		{
			prop: "entryStatus",
			label: "入库状态",
			width: 80,
		},
		{
			prop: "opt",
			fixed: "right",
			label: "操作",
			align: "center",
			width: 160,
			render: (row) => {
				const optArr = [];
				const detailBtn = h(resolveComponent("wp-tip-button"), {
					link: true,
					content: "详情",
					type: "text",
					icon: "wp-icon-detail",
					onClick: () => {
						this.handleOpt("detail", row);
					},
				});

				const checkBtn = h(resolveComponent("wp-tip-button"), {
					link: true,
					content: "入库审核",
					type: "text",
					icon: "wp-icon-pass",
					onClick: () => {
						this.handleOpt("check", row);
					},
				});
				const cancelBtn = h(resolveComponent("wp-tip-button"), {
					link: true,
					content: "取消入库",
					type: "text",
					icon: "wp-icon-cancel",
					onClick: () => {
						this.handleOpt("cancel", row);
					},
				});

				optArr.push(detailBtn);
				optArr.push(checkBtn);
				optArr.push(cancelBtn);

				return h("div", null, optArr);
			},
		},
	];
};
