import { h, resolveComponent } from "vue";
import commonEnum from "@/api/moudles/dm/common";
import uploadConfig from "@/config/upload";
import { permission } from "@/utils/index";

const imageDefault = require("@/assets/images/common/img_instrument_default.svg");

// 搜索model
export const listQuery = {
	name: "",
	code: "",
	barCode: "",
	buildId: "",
	specs: "",
	produceName: "",
	supplierName: "",
	countriesArea: "",
	applyType: "",
	controlMode: "",
	belongOrgId: "",
	belongDeptId: "",
	applyByName: "",
	entryStatus: "",
};

// 搜索表单配置
export const searchConfig = function () {
	return {
		list: [
			{
				type: "input",
				label: "名称搜索",
				model: "name",
				options: {
					placeholder: "请输入资产名称",
				},
			},
			{
				type: "datetimerange",
				label: "登记时间",
				model: "createdAt",
				options: {},
			},
			{
				type: "select",
				label: "资产状态",
				model: "status",
				options: {
					options: commonEnum.deviceStatus,
				},
			},
			{
				type: "select",
				label: "入库状态",
				model: "entryStatus",
				options: {
					options: [
						{ value: "APPROVE", label: "待审核" },
						{ value: "APPROVED", label: "已审核" },
						{ value: "REJECTED", label: "已拒绝" },
						{ value: "CANCELED", label: "已取消" },
						{ value: "CONFIRMED", label: "已确认" },
					],
				},
			},
		],
		config: {
			size: "default",
			onSearch: () => {
				this.handleSubmit();
			},
			model: this.listQuery,
		},
	};
};

export const solutionImageSrc = function (fileId) {
	return uploadConfig.solutionImageSrc(fileId);
};
// 表格展示配置
export const tableColumns = function () {
	return [
		{
			type: "checkbox",
			fixed: "center",
		},
		{
			prop: "fileId",
			label: "图片",
			width: 120,
			render: (row) => {
				let fileIds = row.imageFileRelList;
				let url = [];
				for (let index in fileIds) {
					let httpUrl = solutionImageSrc(fileIds[index]["fileId"]);
					url.push(httpUrl);
				}
				if (url.length <= 0) {
					return h(
						resolveComponent("el-image"),
						{
							src: imageDefault,
							style: {
								height: "40px",
								width: "40px",
							},
						},
						""
					);
				} else {
					return h(
						resolveComponent("el-image"),
						{
							src: url[0],
							"preview-src-list": url,
							"preview-teleported": true,
							style: {
								height: "40px",
								width: "40px",
							},
						},
						""
					);
				}
			},
		},
		{
			prop: "code",
			label: "资产编号",
			width: 120,
		},
		{
			prop: "name",
			label: "资产名称",
			width: 150,
		},
		{
			prop: "categoryName",
			label: "资产类别",
			width: 120,
		},
		{
			prop: "specs",
			label: "规格型号",
			width: 120,
		},
		{
			prop: "quantity",
			label: "数量",
			width: 80,
		},
		{
			prop: "unitPrice",
			label: "单价",
			width: 100,
		},
		{
			prop: "totalPrice",
			label: "总价",
			width: 100,
		},
		{
			prop: "supplierName",
			label: "供应商",
			width: 120,
		},
		{
			prop: "purchaseAt",
			label: "购买日期",
			width: 100,
		},
		{
			prop: "belongDeptName",
			label: "所属部门",
			width: 120,
		},
		{
			prop: "useDeptName",
			label: "使用部门",
			width: 120,
		},
		{
			prop: "applyByName",
			label: "申请人",
			width: 100,
		},
		{
			prop: "approveByName",
			label: "审核人",
			width: 100,
		},
		{
			prop: "status",
			label: "资产状态",
			width: 80,
			formatter: (row) => {
				return row.status === "ENABLED" ? "启用" : "禁用";
			},
		},
		{
			prop: "entryStatus",
			label: "入库状态",
			width: 80,
			render: (row) => {
				const statusMap = {
					APPROVE: { label: "待审核", color: "#59A1FF" },
					APPROVED: { label: "已审核", color: "#48C7A2" },
					REJECTED: { label: "已拒绝", color: "#FF636F" },
					CANCELED: { label: "已取消", color: "#A0A0A0" },
					CONFIRMED: { label: "已确认", color: "#48C7A2" }
				};
				const status = statusMap[row.entryStatus] || { label: "", type: "" };
				return h(
					resolveComponent("el-tag"),
					{ color: status.color, effect: "dark", round: true },
					status.label
				);
			},
		},
		{
			prop: "opt",
			fixed: "right",
			label: "操作",
			align: "center",
			width: 160,
			render: (row) => {
				const optArr = [];
				const detailBtn = h(resolveComponent("wp-tip-button"), {
					link: true,
					content: "详情",
					type: "text",
					icon: "wp-icon-detail",
					onClick: () => {
						this.handleOpt("detail", row);
					},
				});

				const editBtn = h(resolveComponent("wp-tip-button"), {
					content: "编辑",
					type: "text",
					icon: "wp-icon-edit",
					onClick: () => {
						this.handleOpt("edit", row);
					},
				});

				const enableBtn = h(resolveComponent("wp-tip-button"), {
					content: "启用",
					type: "text",
					icon: "wp-icon-enable",
					onClick: () => {
						this.handleOpt("enable", row);
					},
				});

				const disableBtn = h(resolveComponent("wp-tip-button"), {
					content: "停用",
					type: "text",
					icon: "wp-icon-deactive",
					link: true,
					onClick: () => {
						this.handleOpt("disable", row);
					},
				});
				const deleteBtn = h(resolveComponent("wp-tip-button"), {
					content: "删除",
					type: "text",
					icon: "wp-icon-delete",
					link: true,
					onClick: () => {
						this.handleOpt("delete", row);
					},
				});

				const applyAgainBtn = h(resolveComponent("wp-tip-button"), {
					content: "重新入库",
					type: "text",
					icon: "wp-icon-warehousing",
					link: true,
					onClick: () => {
						this.handleOpt("applyAgain", row);
					},
				});

				const confirmBtn = h(resolveComponent("wp-tip-button"), {
					content: "确认入库",
					type: "text",
					icon: "wp-icon-check",
					link: true,
					onClick: () => {
						this.handleOpt("confirm", row);
					},
				});

				optArr.push(detailBtn);
				if (row.entryStatus === "APPROVED") {
					optArr.push(confirmBtn);
					optArr.push(editBtn);
				} else if (row.entryStatus === "CANCELED" || row.entryStatus === "REJECTED") {
					optArr.push(applyAgainBtn);
					optArr.push(deleteBtn);
				} else if (row.entryStatus === "CONFIRMED") {
					switch (row.status) {
						case "ENABLED":
							optArr.push(disableBtn);
							break;
						case "DISABLED":
							optArr.push(enableBtn);
							break;
						default:
					}
				}
				if (optArr && optArr.length > 0) {
					return h("div", null, permission(optArr));
				}
			},
		},
	];
};
