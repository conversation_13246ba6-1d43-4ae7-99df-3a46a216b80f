<template>
	<el-dialog
		v-model="dialogVisible"
		:title="title"
		:visible="dialogVisible"
		width="30%"
	>
		<div class="context">
			{{ content }}
		</div>
		<template #footer>
			<div class="dialog-footer">
				<el-button @click="handleDetail">查看详情</el-button>
				<el-button @click="handleClose">取消</el-button>
				<el-button type="primary" @click="handleOpt">确认</el-button>
			</div>
		</template>
	</el-dialog>
</template>
<script>
export default {
	name: "htDialog",
	data() {
		return {
			dialogVisible: false,
			title: "提示",
			content: "",
			row: {},
		};
	},
	methods: {
		handleClose() {
			this.closed();
			this.alertInfo();
		},
		closed() {
			this.dialogVisible = false;
			this.$emit("closed");
		},
		alertInfo() {
			this.$message.info("已取消操作");
		},
		open(row) {
			this.row = row;
			this.title = row.title;
			this.content = row.content;
			this.dialogVisible = true;
			return this;
		},
		async handleOpt() {
			let result;
			let param = {};
			param.id = this.row.id;

			switch (this.row.dialogType) {
				case "disable":
					result = await this.$API["am/assetInfo"].suspend.post(param);
					break;
				case "enable":
					result = await this.$API["am/assetInfo"].enable.post(param);
					break;
				case "applyAgain":
					result = await this.$API["am/assetInfo"].applyAgain.post(param);
					break;
				case "cancel":
					result = await this.$API["am/assetInfo"].cancel.post(param);
					break;
				case "delete":
					result = await this.$API["am/assetInfo"].delete.delete([param.id]);
					break;
				case "confirm":
					result = await this.$API["am/assetInfo"].confirm.post(param);
					break;
				default:
			}

			if (result && result.success) {
				this.closed();
				this.$message.success(result.msg || "操作成功");
				this.$emit("success");
			} else if (result) {
				this.$message.error(result.msg || "操作失败");
			}
		},
		handleDetail() {
			this.closed();
			this.$emit("openDetail", this.row);
		},
	},
};
</script>

<style lang="scss" scoped>
.dialog-footer {
	display: flex;
	justify-content: flex-end;
	align-items: center;

	.el-button {
		margin-left: 10px;
	}
}
</style>
