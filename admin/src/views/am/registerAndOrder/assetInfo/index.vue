<template>
	<el-container>
		<el-aside width="auto">
			<el-container>
				<el-header style="padding-top: 10px">
					<el-input
						v-model.trim="filterText"
						clearable
						placeholder="输入关键字过滤"
					></el-input>
				</el-header>
				<el-main class="nopadding">
					<el-tree
						ref="categoryTreeRef"
						:data="categoryTreeData"
						:props="defaultProps"
						:filter-node-method="filterNode"
						node-key="id"
						:expand-on-click-node="false"
						default-expand-all
						:highlight-current="true"
						class="menu"
						@node-click="handleNodeClick"
					>
						<template #default="{ node, data }">
							<span class="custom-tree-node">
								<span>{{ node.label }}</span>
							</span>
						</template>
					</el-tree>
				</el-main>
			</el-container>
		</el-aside>

		<!-- Right Side: Main Content -->
		<el-container>
			<el-header>
				<wp-search-form :data="searchConfig"></wp-search-form>
			</el-header>
			<!-- Removed original search header -->
			<el-header style="padding-top: 13px; border-bottom: 1px solid #eee">
				<el-button type="primary" @click="add">资产入库</el-button>
			</el-header>
			<el-main class="nopadding">
				<wpTable
					ref="table"
					:apiObj="apiObj"
					:column="columns"
					:params="listQuery"
					row-key="id"
					stripe
					@selection-change="handleSelectionChange"
				>
					<template #status="scope">
						<el-tag
							v-if="scope.row.status === 'ENABLED'"
							color="#48C7A2"
							effect="dark"
							round
						>
							{{ searchStatus(scope.row.status) }}
						</el-tag>
						<el-tag
							v-if="scope.row.status === 'DISABLED'"
							color="#FF636F"
							effect="dark"
							round
						>
							{{ searchStatus(scope.row.status) }}
						</el-tag>
						<el-tag
							v-if="scope.row.status === 'SCRAPED'"
							color="#F0AD57"
							effect="dark"
							round
						>
							{{ searchStatus(scope.row.status) }}
						</el-tag>
						<el-tag
							v-if="scope.row.status === 'LOSS'"
							color="#A0A0A0"
							effect="dark"
							round
						>
							{{ searchStatus(scope.row.status) }}
						</el-tag>
						<el-tag
							v-if="scope.row.status === 'TO_SCRAPE'"
							color="#D19275"
							effect="dark"
							round
						>
							{{ searchStatus(scope.row.status) }}
						</el-tag>
					</template>
					<template #entryStatus="scope">
						<el-tooltip
							v-if="scope.row.entryStatus === 'APPROVE'"
							class="item"
							content="入库审核"
							effect="dark"
							placement="top"
						>
							<el-button
								circle
								icon="wp-icon-check"
								type="text"
								@click="handleOpt('check', scope.row)"
							/>
						</el-tooltip>
					</template>
				</wpTable>
			</el-main>
		</el-container>

		<!-- Modals remain the same -->
		<edit-modal
			v-if="visible"
			ref="EditModal"
			@closed="visible = !visible"
			@success="handleSubmit"
		></edit-modal>
		<detail-modal
			v-if="showDetail"
			ref="DetailModal"
			@closed="showDetail = !showDetail"
			@success="handleSubmit"
		></detail-modal>
		<ht-dialog
			v-if="showDialog"
			ref="HtDialog"
			@closed="showDialog = !showDialog"
			@openDetail="openDetail"
			@success="handleSubmit"
		></ht-dialog>
	</el-container>
</template>

<script>
import {listQuery, searchConfig, tableColumns} from "./config";
import EditModal from "./edit";
import DetailModal from "./detail";
import HtDialog from "./dialog";
import commonEnum from "@/api/moudles/am/common";

export default {
	components: {
		EditModal,
		DetailModal,
		HtDialog,
	},
	name: "index",
	data() {
		return {
			apiObj: this.$API["am/assetInfo"].page,
			columns: tableColumns.bind(this)(),
			searchConfig: null,
			listQuery: Object.assign({}, listQuery),
			selections: [],
			visible: false,
			showDetail: false,
			dialogVisible: false,
			showDialog: false,
			categoryTreeData: [],
			filterText: "",
			defaultProps: {
				children: "children",
				label: "name",
			},
		};
	},
	watch: {
		filterText(val) {
			this.$refs.categoryTreeRef.filter(val);
		},
	},
	methods: {
		async getCategoryTree() {
			try {
				const res = await this.$API["am/category"].getEnabledCategoryTree.get("AC");
				if (res.success) {
					res.data.id = null;
					this.categoryTreeData = [res.data];
				} else {
					this.$message.error("获取分类失败: " + res.msg);
					this.categoryTreeData = [{id: null, name: "全部分类"}];
				}
			} catch (error) {
				console.error("Error fetching category tree:", error);
				this.$message.error("获取分类时出错");
				this.categoryTreeData = [{id: null, name: "全部分类"}];
			}
		},
		filterNode(value, data) {
			if (!value) return true;
			return data.name.includes(value);
		},
		handleNodeClick(data) {
			this.listQuery.categoryId = data.id;
			this.handleSubmit();
		},
		clearCategoryFilter() {
			this.listQuery.categoryId = null;
			this.$refs.categoryTreeRef.setCurrentKey(null);
			this.handleSubmit();
		},
		handleSubmit() {
			this.$refs.table.upData(this.listQuery);
		},
		openDetail(row) {
			this.handleOpt("detail", row);
		},
		handleSelectionChange(val) {
			this.selections = val;
		},
		async handleOpt(type, row) {
			console.log("row===>", row);
			let result = null;
			row.dialogType = type;
			switch (type) {
				case "detail":
					this.showDetail = true;
					result = await this.$API["am/assetInfo"].getWithDetail.get(
						row.id
					);
					this.$nextTick(() => {
						if (result.success) {
							this.$refs.DetailModal.open({
								title: "资产详情",
							}).setData(result.data);
						}
					});
					break;
				case "edit":
					this.visible = true;
					result = await this.$API["am/assetInfo"].getWithDetail.get(
						row.id
					);
					this.$nextTick(() => {
						this.$refs.EditModal.open({
							title: "资产入库",
						}).setData(result.data);
					});
					break;
				case "confirm":
					row.content = "确认入库后，该资产将正式入库，是否继续操作？";
					row.title = "确认入库";
					this.showDialog = true;
					this.$nextTick(() => {
						this.$refs.HtDialog.open(row);
					});
					break;
				case "cancel":
					row.content = "确认取消资产入库嘛？";
					row.title = "取消入库";
					this.showDialog = true;
					this.$nextTick(() => {
						this.$refs.HtDialog.open(row);
					});
					break;
				case "disable":
					row.content =
						"停用后，该资产将无法被使用，是否继续操作？";
					row.title = "资产停用";
					this.showDialog = true;
					this.$nextTick(() => {
						this.$refs.HtDialog.open(row);
					});
					break;
				case "enable":
					row.content = "确认启用当前资产嘛？";
					row.title = "资产启用";
					this.showDialog = true;
					this.$nextTick(() => {
						this.$refs.HtDialog.open(row);
					});
					break;
				case "applyAgain":
					row.content = "确认重新入库当前资产吗？";
					row.title = "重新入库";
					this.showDialog = true;
					this.$nextTick(() => {
						this.$refs.HtDialog.open(row);
					});
					break;
				case "delete":
					row.content = "确认删除该资产记录吗？删除后无法恢复！";
					row.title = "删除资产";
					this.showDialog = true;
					this.$nextTick(() => {
						this.$refs.HtDialog.open(row);
					});
					break;
				default:
			}
		},
		add() {
			this.visible = true;
			this.$nextTick(() => {
				this.$refs.EditModal.open({
					title: "资产入库",
				}).setData({categoryId: this.listQuery.categoryId});
			});
		},
		searchStatus(status) {
			return commonEnum.searchDeviceStatus(status);
		},
	},

	computed: {},
	created() {
		this.searchConfig = searchConfig.bind(this)();
	},
	mounted() {
		this.getCategoryTree();
	},
};
</script>
