<template>
	<el-container>
		<el-header>
			<div class="title">
				<div class="title-back" @click="goBack">
					<div class="title-back-arrow">
						<img :src="backImgSrc" alt="<-"/>
					</div>
					<span class="title-back-span">返回</span>
				</div>
				<h4 class="title-content">盘点清单</h4>
			</div>
		</el-header>
		<el-header class="detail-content">
			<div class="detail-content-title">
				<div class="title-line"></div>
				<div class="title-span">
					{{ orgName }}
				</div>
			</div>
		</el-header>
		<el-main class="nopadding">
			<wpTable
				ref="table"
				:column="columns"
				:data="tabelData"
				hidePagination
				row-key="id"
				stripe
			>
				<template #fBtns>
					<el-button
						v-if="type !== 'view'"
						type="primary"
						@click="complete"
					>完成盘点
					</el-button
					>
				</template>
				<!-- 盘点数量输入框 -->
				<template v-if="type !== 'view'" #checkQuantity="scope">
					<el-input
						v-model.number="scope.row.checkQuantity"
						type="number"
						:min="0"
						:max="scope.row.quantity"
					></el-input>
				</template>
				<template v-else #checkQuantity="scope">
					<el-input
						v-model.number="scope.row.checkQuantity"
						readonly
						:disabled="true"
					></el-input>
				</template>

				<!-- 盘点状态选择框 -->
				<template v-if="type !== 'view'" #status="scope">
					<el-select v-model="scope.row.status" placeholder="请选择盘点状态">
						<el-option label="正常" value="NORMAL"></el-option>
						<el-option label="异常" value="ABNORMAL"></el-option>
					</el-select>
				</template>
				<template v-else #status="scope">
					<el-tag :type="getStatusType(scope.row.status)">
						{{ getStatusText(scope.row.status) }}
					</el-tag>
				</template>

				<!-- 处理状态展示 -->
				<template #processStatus="scope">
					<el-tag :type="getProcessStatusType(scope.row.processStatus)">
						{{ getProcessStatusText(scope.row.processStatus) }}
					</el-tag>
				</template>

				<!-- 盘点结果说明输入框 -->
				<template v-if="type !== 'view'" #checkResult="scope">
					<el-input
						v-model="scope.row.checkResult"
						type="textarea"
						:rows="2"
						placeholder="请输入盘点结果说明"
					></el-input>
				</template>
				<template v-else #checkResult="scope">
					<span>{{ scope.row.checkResult || '-' }}</span>
				</template>

				<!-- 处理建议输入框 -->
				<template v-if="type !== 'view'" #processSuggestion="scope">
					<el-input
						v-model="scope.row.processSuggestion"
						type="textarea"
						:rows="2"
						placeholder="请输入处理建议"
					></el-input>
				</template>
				<template v-else #processSuggestion="scope">
					<span>{{ scope.row.processSuggestion || '-' }}</span>
				</template>
			</wpTable>
		</el-main>
		<abnormal-form
			v-if="showAbnormalDialog"
			ref="AbnormalForm"
			@closed="showAbnormalDialog = !showAbnormalDialog"
			@upData="getData"
		></abnormal-form>
	</el-container>
</template>

<script>
import backImgSrc from "@/assets/images/common/details_icon_back.svg";
import { h, resolveComponent } from "vue";
import AbnormalForm from "./abnormalForm.vue";

export default {
	name: "amInventoryChecking",
	components: {
		AbnormalForm
	},
	data() {
		let inventoryId = "",
			orgName = "";
		if (this.$route && this.$route.query.inventoryId) {
			inventoryId = this.$route.query.inventoryId;
		}
		if (this.$route && this.$route.query.orgName) {
			orgName = this.$route.query.orgName;
		}
		return {
			backImgSrc,
			inventoryId,
			orgName,
			type: this.$route.query.type || "",
			showAbnormalDialog: false,
			columns: [
				{label: "部门", prop: "orgName"},
				{label: "仪器设备名称", prop: "assetName"},
				{label: "规格", prop: "assetSpec"},
				{label: "现存数量", prop: "quantity"},
				{label: "盘点数量", prop: "checkQuantity"},
				{
					label: "盘点状态",
					prop: "status",
					formatter: (row) => {
						const statusMap = {
							'NORMAL': '正常',
							'ABNORMAL': '异常'
						};
						return statusMap[row.status] || row.status;
					}
				},
				{
					label: "处理状态",
					prop: "processStatus",
					formatter: (row) => {
						const statusMap = {
							'UNPROCESSED': '未处理',
							'PROCESSING': '处理中',
							'PROCESSED': '已处理'
						};
						return statusMap[row.processStatus] || row.processStatus;
					}
				},
				// {label: "盘点结果说明", prop: "checkResult"},
				// {label: "处理建议", prop: "processSuggestion"},
				{
					prop: "opt",
					fixed: "right",
					label: "操作",
					align: "center",
					width: 180,
					render: (row) => {
						const optArr = [];
						const passBtn = h(resolveComponent("wp-tip-button"), {
							link: true,
							content: "盘点通过",
							type: "text",
							icon: "wp-icon-check",
							onClick: () => {
								this.handlePass(row);
							},
						});

						const abnormalBtn = h(resolveComponent("wp-tip-button"), {
							link: true,
							content: "盘点异常",
							type: "text",
							icon: "wp-icon-abnormal",
							onClick: () => {
								this.handleAbnormal(row);
							},
						});

						if (row.processStatus !== 'PROCESSED') {
							optArr.push(passBtn);
							optArr.push(abnormalBtn);
						}
						// if (row.status !== 'NORMAL' && row.processStatus !== 'PROCESSED') {
						// 	optArr.push(abnormalBtn);
						// }

						return h("div", null, optArr);
					},
				}
			],
			tabelData: [],
		};
	},
	methods: {
		goBack() {
			this.$router.back();
		},
		handleOpt(obj) {
		},
		popLeave(row) {
			switch (row.checkQuantityType) {
				case 1:
					row.checkQuantity = row.quantity;
					row.status = 'NORMAL';
					break;
				case 2:
					row.checkQuantity = 0;
					row.status = 'LOSS';
					break;
				case 3:
					row.checkQuantity = row.subCapaitySub;
					row.status = row.checkQuantity < row.quantity ? 'DAMAGE' : 'NORMAL';
					break;
				default:
					row.checkQuantity = row.subCapaitySub;
					row.status = row.checkQuantity < row.quantity ? 'DAMAGE' : 'NORMAL';
			}
		},
		async handlePass(row) {
			row.status = 'NORMAL';
			row.checkResult = '盘点正常';
			row.processStatus = 'PROCESSED';
			const res = await this.$API["am/inventoryDetail"]
				.editOne
				.post(row);
			if (res.success) {
				this.$message.success("盘点成功");
				this.goBack();
			} else {
				this.$message.error(res.msg);
			}
		},
		handleAbnormal(row) {
			this.showAbnormalDialog = true;
			row.processStatus = 'PROCESSED';
			row.status = 'ABNORMAL';
			this.$nextTick(() => {
				this.$refs.AbnormalForm.openPage(row);
			});
		},
		getStatusType(status) {
			const typeMap = {
				'NORMAL': 'success',  // 绿色
				'LOSS': 'danger',     // 红色
				'DAMAGE': 'warning'   // 黄色
			};
			return typeMap[status] || 'info';
		},

		// 获取盘点状态文本
		getStatusText(status) {
			const textMap = {
				'NORMAL': '正常',
				'ABNORMAL': '异常',
			};
			return textMap[status] || status;
		},

		// 获取处理状态类型（用于标签颜色）
		getProcessStatusType(status) {
			const typeMap = {
				'UNPROCESSED': 'info',    // 灰色
				'PROCESSING': 'primary',  // 蓝色
				'PROCESSED': 'success'    // 绿色
			};
			return typeMap[status] || 'info';
		},

		// 获取处理状态文本
		getProcessStatusText(status) {
			const textMap = {
				'UNPROCESSED': '未处理',
				'PROCESSING': '处理中',
				'PROCESSED': '已处理'
			};
			return textMap[status] || status;
		},
		async complete() {
			let detailList = [];
			this.tabelData.forEach((item) => {
				if (typeof item.checkQuantity !== "undefined") {
					detailList.push({
						id: item.id,
						quantity: item.quantity,
						checkQuantity: item.checkQuantity,
						status: item.status || 'NORMAL',
						checkResult: item.checkResult || '',
						processSuggestion: item.processSuggestion || '',
						processStatus: 'PROCESSED'
					});
				}
			});
			const res = await this.$API["am/inventoryDetail"]
				.edit(this.inventoryId)
				.post(detailList);
			if (res.success) {
				this.$message.success("盘点成功");
				this.goBack();
			} else {
				this.$message.error(res.msg);
			}
		},
		async getData() {
			const res = await this.$API["am/inventoryDetail"]
				.page(this.inventoryId)
				.get({
					pageNum: 1,
					pageSize: 5000,
				});
			if (res.success) {
				this.tabelData = res.data.list;
			}
		},
	},
	mounted() {
		this.getData();
	},
};
</script>

<style lang="scss" scoped>
.panel {
	display: inline-block;

	:deep(.sc-status-processing) {
		margin-right: 5px;
	}
}

.radioGroup {
	text-align: left;
	height: 140px;
	padding: 0 10px 10px 10px;

	.el-radio {
		width: 100%;

		&:last-of-type {
			:deep(.el-radio__label) {
				margin-top: 30px;
			}
		}
	}
}

.title {
	background-color: #fff;
	width: 100%;
	height: 58px !important;
	padding: 0;
	display: flex;
	align-items: center;
	//border-bottom: 1px solid #f0f0f0;

	.title-back {
		display: flex;
		align-items: center;
		justify-content: center;
		cursor: pointer;

		.title-back-arrow {
			img {
				vertical-align: middle;
				width: 16px;
				height: 16px;
			}
		}

		.title-back-span {
			margin-left: 8px;
			font-size: 14px;
			font-weight: 400;
			color: #555555;
		}
	}

	.title-content {
		width: calc(100% - 56px);
		text-align: center;
		font-size: 18px;
		font-weight: 550;
		color: #333333;
	}
}

// 详情中 内容 展示
.detail-content {
	height: 100%;

	.detail-content-title {
		padding: 24px;
		// border-bottom: 1px solid #f0f0f0;
		display: flex;
		align-items: center;
		padding-left: 0;

		// 内容部分 标题
		.title-line {
			width: 3px;
			height: 16px;
			background: linear-gradient(
					180deg,
					#009fa8 0%,
					#0c8ec0 55%,
					#197dd9 100%
			);
			border-radius: 0px 0px 0px 0px;
		}

		.title-span {
			font-size: 16px;
			font-family: Source Han Sans CN-Regular, Source Han Sans CN;
			font-weight: 400;
			color: #333333;
			margin-left: 12px;
		}
	}
}
</style>
