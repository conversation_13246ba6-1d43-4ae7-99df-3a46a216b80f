import { h, resolveComponent } from "vue";
import commonEnum from "@/api/moudles/dm/common";

// 搜索model
export const listQuery = {
	keyword: "",
	planAt: "",
	status: "",
	orgId: "",
};

// 搜索表单配置
export const searchConfig = function () {
	return {
		list: [
			{
				type: "input",
				label: "盘点编号",
				model: "keyword",
				width: "10",
				options: {},
			},
			{
				type: "input",
				label: "盘点人员",
				model: "keyName",
				width: "10",
				options: {},
			},
			{
				label: "部门",
				type: "inputTree",
				model: "orgId",
				width: "10",
				options: {
					request: this.$API["uc/org"].getOrgTree.get,
					items: [],
					props: {
						children: "child",
						label: "name"
					},
				},
			},
			{
				type: "datetimerange",
				label: "计划盘点时间",
				model: "planAt",
				options: {},
			},
			{
				type: "select",
				label: "状态",
				model: "status",
				options: {
					options: commonEnum.inventoryStatus,
				},
			},
		],
		config: {
			size: "default",
			onSearch: () => {
				this.handleSubmit();
			},
			model: this.listQuery,
		},
	};
};

// 表格展示配置
export const tableColumns = function () {
	return [
		{
			type: "checkbox",
			fixed: "center",
		},
		{
			prop: "code",
			label: "编号",
			width: 180,
		},
		{
			prop: "orgName",
			label: "部门",
			width: 150,
		},
		{
			prop: "createdByName",
			label: "创建人",
			width: 120,
		},
		{
			prop: "actByName",
			label: "盘点人",
			width: 120,
		},
		{
			prop: "planAt",
			label: "计划盘点时间",
			width: 150,
		},
		{
			prop: "inventoryAt",
			label: "盘点开始时间",
			width: 150,
		},
		{
			prop: "inventoryEndAt",
			label: "盘点结束时间",
			width: 150,
		},
		{
			prop: "normalCount",
			label: "正常数量",
			width: 100,
		},
		{
			prop: "abnormalCount",
			label: "异常数量",
			width: 100,
		},
		{
			prop: "status",
			label: "盘点状态",
			width: 100,
		},
		{
			prop: "opt",
			fixed: "right",
			label: "操作",
			align: "center",
			width: 180,
			render: (row) => {
				const optArr = [];
				const startBtn = h(resolveComponent("wp-tip-button"), {
					link: true,
					content: "开始盘点",
					type: "text",
					icon: "wp-icon-device-inventory",
					onClick: () => {
						this.handleOpt("start", row);
					},
				});

				const viewBtn = h(resolveComponent("wp-tip-button"), {
					link: true,
					content: "查看详情",
					type: "text",
					icon: "wp-icon-detail",
					onClick: () => {
						this.handleOpt("view", row);
					},
				});

				const continueBtn = h(resolveComponent("wp-tip-button"), {
					link: true,
					content: "继续盘点",
					type: "text",
					icon: "wp-icon-device-inventory",
					onClick: () => {
						this.handleOpt("start", row);
					},
				});

				const canceldBtn = h(resolveComponent("wp-tip-button"), {
					link: true,
					content: "取消盘点",
					type: "text",
					icon: "wp-icon-cancel",
					onClick: () => {
						this.handleOpt("cancel", row);
					},
				});

				if (row.status === "DRAFT") {
					optArr.push(startBtn);
				}

				if (row.status === "CHECKING") {
					optArr.push(continueBtn);
				}

				if (row.status === "DRAFT" || row.status === "CHECKING") {
					optArr.push(canceldBtn);
				}

				if (row.status === "FINISHED") {
					optArr.push(viewBtn);
				}

				return h("div", null, optArr);
			},
		},
	];
};
